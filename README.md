Adding new profile:

Edit
braveprofiles.json

add field
"profile_name": {
            "urls": ["https://profile_url/"], #Modify
            "profile_dir": "Profile XX",      #Modify
            "options": {
                "start_maximized": true,
                "disable_features": [],
                "devtools": false,
                "no_default_browser_check": true,
                "incognito": false
            }
        },

Add .ico of the profile to directory Avatars with same name as profile_name
.ico -> /Avatars profile_name.png

RUN
generate_bats(profiles, paths.batchs_folder)
exit()

Run batch file to create required profile directory in Brave profile directory location.
In Brave -> name window

Run the rest.

