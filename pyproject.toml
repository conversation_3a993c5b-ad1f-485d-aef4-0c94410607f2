[project]
name = "brave-profiles-w-test-main"
version = "0.1.0"
description = "Creates Brave profiles"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "mypy>=1.16.1",
    "pipreqs>=0.5.0",
    "psutil==7.0.0",
    "python-dotenv==1.1.1",
    "requests==2.32.4",
    "ruff>=0.12.0",
]

[dependency-groups]
dev = [
    "types-psutil>=7.0.0.20250601",
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"
