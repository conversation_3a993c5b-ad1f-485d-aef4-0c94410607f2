# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a Python application for managing Brave browser profiles with automated configuration and launching capabilities. The system generates shell scripts for launching specific Brave profiles with predefined URLs and settings.

### Core Components

- **bat_edit.py**: Main entry point that orchestrates profile management, generates launch scripts, and updates Brave configuration files
- **config.py**: Path configuration using hardcoded absolute paths (BravePaths dataclass)
- **brave_profiles.json**: Central configuration defining profiles, URLs, and browser options
- **Profile Management**: Handles Local State and Preferences files in Brave's configuration directory
- **Shell Script Generation**: Creates executable .sh files in batchs/ folder for launching profiles

### Key Data Flow

1. Load profile configurations from JSON files
2. Generate executable shell scripts for each profile
3. Kill existing Brave processes
4. Update Brave's Local State and Preferences files
5. Profile scripts can then be executed to launch specific Brave instances

## Development Commands

### Running the Application
```bash
python main.py  # Currently just prints hello message
python brave_profiles_main/bat_edit.py  # Main profile management functionality
```

### Python Environment
```bash
# Install dependencies
pip install -r requirements.txt
# or with uv (project uses uv.lock)
uv sync

# Type checking
mypy .

# Linting
ruff check .
ruff format .
```

### Testing
```bash
python -m pytest test/
```

## Configuration Files

- **brave_profiles.json**: Main profile configuration with URLs and browser options
- **brave_start_options.json**: Boolean option mappings for command-line flags
- **pyproject.toml**: Python project configuration with dependencies

## Important Notes

- Uses hardcoded absolute paths in config.py that need adjustment for different environments
- Requires sudo permissions for killing Brave processes
- Shell scripts are generated for Linux (/usr/bin/brave-browser)
- Profile avatars are managed in Avatars/ directory with PNG files
- Backup functionality for Local State and Preferences files