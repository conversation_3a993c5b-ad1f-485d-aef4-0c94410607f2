import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

import json
from tempfile import TemporaryDirectory
from brave_profiles_main.config import BravePaths
from brave_profiles_main.search_prefs import force_update_local_state
from brave_profiles_main.profile_types import Profile

def make_mock_local_state(profiles: dict) -> dict:
    return {
        "profile": {
            "info_cache": {
                profile.profile_dir: {
                    "name": profile.name,
                    "is_using_default_name": True,
                    "gaia_picture_file_name": "some.png",
                    "use_gaia_picture": True,
                    "has_migrated_to_gaia_info": True,
                }
                for profile in profiles.values()
            }
        }
    }

def test_force_update_local_state():
    profiles = {
        "Profile 1": Profile(profile_dir="Profile 1", name="scraping"),
        "Profile 2": Profile(profile_dir="Profile 2", name="chatgpt"),
    }

    with TemporaryDirectory() as tmpdir:
        tmp_path = Path(tmpdir)
        brave_config = tmp_path / ".config/BraveSoftware/Brave-Browser"
        brave_config.mkdir(parents=True)
        local_state_path = brave_config / "Local State"

        # Write mock local state
        state_data = make_mock_local_state(profiles)
        with local_state_path.open("w", encoding="utf-8") as f:
            json.dump(state_data, f)

        # ✅ Create test BravePaths that points to the temp dir
        test_paths = BravePaths(root=tmp_path)

        # Run update
        result = force_update_local_state(profiles, view_only=False, paths=test_paths)

        # Load updated state
        print(local_state_path)
        with local_state_path.open("r", encoding="utf-8") as f:
            new_state = json.load(f)

        for profile in profiles.values():
            pdata = new_state["profile"]["info_cache"][profile.profile_dir]
            assert pdata["name"] == profile.name
            assert pdata["is_using_default_name"] is False

        assert result == {p.profile_dir: p.name for p in profiles.values()}
        assert str(local_state_path).startswith(str(tmp_path))
