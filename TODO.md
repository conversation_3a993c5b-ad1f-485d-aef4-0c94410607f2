General Rename session

bat_edit.py -> main.py

search_prefs.py -> more descriptive


New folder to keep safe:
brave_avatar_list.xml
brave_profiles.json
brave_start_options.json

logger to more informative



##############
Automation for adding new profile
command line option to generate batch, run the batch and run the rest
e.g.

main. py --name-of-profile-to-add --profile-url
-> edits braveprofiles.json by adding next field before Default
"name-of-profile-to-add": {
            "urls": ["profile-url"],
            "profile_dir": f"Profile {next_free_profile_nro}",
            "options": {
                "start_maximized": true,
                "disable_features": [],
                "devtools": false,
                "no_default_browser_check": true,
                "incognito": false
            }
        },
runs these
generate_bats(profiles, paths.batchs_folder)
- chmod... -> run as executable               ### not yet implemented
- checks that /Avatars/name-of-profile-to-add.png exists
- runs generated batch to create Profile directory
- shuts down all Braves
- runs the rest

Is it possible to modify email addressess, telephone numbers, addressess ect <NAME_EMAIL>, Mätästie...
Can Profiles share bookmarks?

Port to windows