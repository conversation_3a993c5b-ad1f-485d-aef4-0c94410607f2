#!/bin/bash

set -e

echo "=== Checking if Brave Browser is installed ==="
if dpkg -l | grep -q brave-browser; then
    echo "Brave is installed. Proceeding to remove it."
    sudo apt remove -y brave-browser
    sudo apt purge -y brave-browser
    sudo apt autoremove -y
else
    echo "Brave is not installed. Skipping removal."
fi

echo "=== Cleaning old Brave sources and keys (if any) ==="
sudo rm -f /etc/apt/sources.list.d/brave-browser-release.list
sudo apt-key del 20038257 || true  # Brave's public key ID (may not be present)
sudo rm -f /etc/apt/keyrings/brave-browser.gpg

echo "=== Removing all user-level Brave data ==="

backup_dir="$HOME/Brave_Backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

# List of user directories to clean
brave_paths=(
  "$HOME/.config/BraveSoftware"
  "$HOME/.cache/BraveSoftware"
  "$HOME/.local/share/BraveSoftware"
  "$HOME/.pki/nssdb"
  "$HOME/.config/autostart/brave-browser.desktop"
)

for path in "${brave_paths[@]}"; do
  if [ -e "$path" ]; then
    echo "Backing up and deleting: $path"
    cp -r "$path" "$backup_dir/"
    rm -rf "$path"
  fi
done

echo "All Brave user data removed. Backup (if any) stored in: $backup_dir"


echo "=== Installing prerequisites ==="
sudo apt update
sudo apt install -y curl apt-transport-https gnupg

echo "=== Adding Brave GPG key (using apt-key workaround) ==="
curl -fsSL https://brave-browser-apt-release.s3.brave.com/brave-core.asc | sudo apt-key add -

echo "deb [arch=amd64] https://brave-browser-apt-release.s3.brave.com/ stable main" | \
  sudo tee /etc/apt/sources.list.d/brave-browser-release.list > /dev/null

echo "=== Updating package list and installing Brave ==="
sudo apt update
sudo apt install -y brave-browser

echo "=== Brave Browser is now cleanly installed ==="
