from pathlib import Path
from dataclasses import dataclass

@dataclass
class BravePaths:
    root: Path = Path.home()
    base_dir: Path = Path(__file__).resolve().parent

    @property
    def icons_folder(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/icons')

    @property
    def batchs_folder(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/batchs')

    @property
    def local_state_backup_folder(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/local_state_backup')

    @property
    def preferences_backup_folder(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/preferences_backup')

    @property
    def avatars_local_folder(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/Avatars')

    @property
    def avatar_list_file_path(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/brave_avatar_list.xml')

    @property
    def profiles_json_file(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/brave_profiles.json')

    @property
    def profiles_booleans_file(self) -> Path:
        return Path(r'/home/<USER>/Documents/brave_profiles_w_test-main/brave_profiles_main/brave_start_options.json')

    @property
    def brave_avatar_dest_folder(self) -> Path:
        return Path(r'/home/<USER>/.config/BraveSoftware/Brave-Browser/Avatars')

    @property
    def brave_user_folder(self) -> Path:
        return Path(r'/home/<USER>/.config/BraveSoftware/Brave-Browser')

    @property
    def brave_local_state_file_path(self) -> Path:
        return Path(r'/home/<USER>/.config/BraveSoftware/Brave-Browser/Local State')

    @property
    def brave_exe_file_path(self) -> Path:
        return Path(r'/usr/bin/brave-browser')

    @staticmethod
    def this_file() -> Path:
        return Path(__file__)

def get_all_paths(instance: BravePaths) -> dict[str, Path]:
    import inspect
    from dataclasses import fields
    cls = type(instance)
    result = {}

    # Get regular dataclass fields
    for field in fields(cls):
        result[field.name] = getattr(instance, field.name)

    # Get @property methods
    for name, member in inspect.getmembers(cls):
        if isinstance(member, property):
            try:
                result[name] = getattr(instance, name)
            except Exception as e:
                result[name] = f'<Error accessing property: {e}>'

    return result
