import json
from pathlib import Path
from typing import Any, Dict, List, Union

# Type alias for JSON data structures
JsonValue = Union[Dict[str, Any], List[Any], str, int, float, bool, None]


def write_json_file(file_path: Path, data: JsonValue, compact: bool = True) -> bool:
    """
    Write JSON data to a file with proper error handling.

    Args:
        file_path: Path to the JSON file
        data: Data to write (must be JSON serializable)
        compact: If True, writes compact JSON without whitespace

    Returns:
        bool: True if successful, False if failed
    """
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            if compact:
                json.dump(data, f, separators=(",", ":"))
            else:
                json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error writing JSON file {file_path}: {e}")
        return False


def read_json_file(file_path: Path) -> JsonValue:
    """
    Read JSON data from a file with proper error handling.

    Args:
        file_path: Path to the JSON file to read

    Returns:
        JsonValue: The parsed JSON data

    Raises:
        FileNotFoundError: If the file doesn't exist
        json.JSONDecodeError: If the file contains invalid JSON
        OSError: If there are file access issues
    """
    try:
        with open(file_path, encoding="utf-8") as f:
            data = json.load(f)
            return data
    except FileNotFoundError:
        print(f"Configuration file {file_path} not found")
        raise
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in configuration file: {e}")
        raise
    except OSError as e:
        print(f"Error reading file {file_path}: {e}")
        raise


def read_json_file_safe(file_path: Path, default: JsonValue = None) -> JsonValue:
    """
    Read JSON data from a file with safe error handling that returns a default value.

    Args:
        file_path: Path to the JSON file to read
        default: Default value to return if reading fails

    Returns:
        JsonValue: The parsed JSON data or default value if reading fails
    """
    try:
        return read_json_file(file_path)
    except (FileNotFoundError, json.JSONDecodeError, OSError) as e:
        print(f"Failed to read JSON file {file_path}: {e}")
        return default


def validate_json_dict(data: JsonValue, file_path: Path) -> Dict[str, Any]:
    """
    Validate that JSON data is a dictionary and return it with proper typing.

    Args:
        data: The JSON data to validate
        file_path: Path to the file (for error messages)

    Returns:
        Dict[str, Any]: The validated dictionary

    Raises:
        TypeError: If the data is not a dictionary
    """
    if not isinstance(data, dict):
        raise TypeError(
            f"Expected JSON object (dict) in {file_path}, got {type(data).__name__}"
        )
    return data


def validate_json_list(data: JsonValue, file_path: Path) -> List[Any]:
    """
    Validate that JSON data is a list and return it with proper typing.

    Args:
        data: The JSON data to validate
        file_path: Path to the file (for error messages)

    Returns:
        List[Any]: The validated list

    Raises:
        TypeError: If the data is not a list
    """
    if not isinstance(data, list):
        raise TypeError(
            f"Expected JSON array (list) in {file_path}, got {type(data).__name__}"
        )
    return data


if __name__ == "__main__":
    # Example usage
    test_data = {"name": "Test", "value": 42, "items": [1, 2, 3]}

    # Write compact JSON
    success = write_json_file(Path("test_compact.json"), test_data)
    print(f"Compact write {'succeeded' if success else 'failed'}")

    # Write pretty JSON
    success = write_json_file(Path("test_pretty.json"), test_data, compact=False)
    print(f"Pretty write {'succeeded' if success else 'failed'}")

    # Read JSON
    data = read_json_file(Path("test_compact.json"))
    print(f"Read data: {data}")
