import json
from pathlib import Path
from typing import Any, Optional


def write_json_file(file_path: Path, data: Any, compact: bool = True) -> bool:
    """
    Write JSON data to a file with proper error handling.

    Args:
        file_path: Path to the JSON file
        data: Data to write (must be JSON serializable)
        compact: If True, writes compact JSON without whitespace

    Returns:
        bool: True if successful, False if failed
    """
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            if compact:
                json.dump(data, f, separators=(",", ":"))
            else:
                json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error writing JSON file {file_path}: {e}")
        return False


def read_json_file(file_path: Path) -> Optional[Any]:
    """
    Read JSON data from a file with proper error handling.

    Args:
        file_path: Path to the JSON file to read

    Returns:
        Optional[Any]: The parsed JSON data if successful, None if failed
    """
    try:
        with open(file_path, encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Configuration file {file_path} not found")
        raise
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in configuration file: {e}")
        raise

    # try:
    #     with open(file_path, "r", encoding="utf-8") as f:
    #         return json.load(f)
    # except Exception as e:
    #     print(f"Error reading JSON file {file_path}: {e}")
    #     return None


if __name__ == "__main__":
    # Example usage
    test_data = {"name": "Test", "value": 42, "items": [1, 2, 3]}

    # Write compact JSON
    success = write_json_file(Path("test_compact.json"), test_data)
    print(f"Compact write {'succeeded' if success else 'failed'}")

    # Write pretty JSON
    success = write_json_file(Path("test_pretty.json"), test_data, compact=False)
    print(f"Pretty write {'succeeded' if success else 'failed'}")

    # Read JSON
    data = read_json_file(Path("test_compact.json"))
    print(f"Read data: {data}")
