from enum import Enum
from pathlib import Path


class PathEnum(Enum):
    ICONS_FOLDER = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/icons"
    )
    BATCHS_FOLDER = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/batchs"
    )
    LOCAL_STATE_BACKUP_FOLDER = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/local_state_backup"
    )
    PREFERENCES_BACKUP_FOLDER = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/preferences_backup"
    )
    AVATARS_LOCAL_FOLDER = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/Avatars"
    )
    AVATAR_LIST_FILE_PATH = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/brave_avatar_list.xml"
    )
    PROFILES_JSON_FILE = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/brave_profiles.json"
    )
    PROFILES_BOOLEANS_FILE = Path(
        r"/home/<USER>/Downloads/brave_profiles_ubuntu_port/brave_profiles_main/brave_start_options.json"
    )
    BRAVE_AVATAR_DEST_FOLDER = Path(
        r"/home/<USER>/.config/BraveSoftware/Brave-Browser/Avatars"
    )
    BRAVE_USER_FOLDER = Path(r"/home/<USER>/.config/BraveSoftware/Brave-Browser")
    BRAVE_LOCAL_STATE_FILE_PATH = Path(
        r"/home/<USER>/.config/BraveSoftware/Brave-Browser/Local State"
    )
    BRAVE_EXE_FILE_PATH = Path(r"/usr/bin/brave-browser")

    @staticmethod
    def this_file() -> Path:
        return Path(__file__)


if __name__ == "__main__":
    for item in PathEnum:
        print(f"item.name: {item.name} item.value: {item.value}")
    print(f"PathEnum.this_file(): {PathEnum.this_file()}")
