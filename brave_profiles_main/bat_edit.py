"""
This script manages Brave browser profiles and generates .bat files from a JSON configuration.

The JSON file should have the following format:
{
    "profiles": {
        "profile_name": {
            "urls": ["https://example.com"],
            "profile_dir": "Profile 1",
            "options": {
                "start_maximized": true,
                "disable_features": ["FingerprintingProtection"],
                "devtools": false,
                "no_default_browser_check": false,
                "incognito": false
            }
        }
    }
}
"""
from pathlib import Path
BASE_DIR = Path(__file__).resolve().parent
import json
import logging
import os
from typing import Dict, List, Optional, Union
from brave_profiles_main.file_utils import make_folder_with_gitkeep
from brave_profiles_main.search_prefs import update_profile_name_in_dir_preferences, \
force_update_local_state, kill_brave, wait_for_brave_shutdown
from brave_profiles_main.profile_types import Profile, ProfileOptions
from brave_profiles_main.json_utils import read_json_file
from urllib.parse import urlparse
import requests
import os
from brave_profiles_main.config import BravePaths, get_all_paths
import sys
# from brave_profiles_main.PathEnum import PathEnum

# Configure logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)       

def load_config(paths: BravePaths) -> Dict[str, Profile]:
    """Load and validate the configuration file."""
    config = read_json_file(paths.profiles_json_file)
    bool_options = read_json_file(paths.profiles_booleans_file)

    profiles = {}
    for name, data in config.get('profiles', {}).items():
        options_dict = {}
        for option, option_value in data.get('options', {}).items():
            if isinstance(option_value, bool) and option_value == True:
                options_dict[option] = bool_options.get(option, ) + ' ^'
            elif isinstance(option_value, bool) and option_value == False:
                options_dict[option] = ''
            elif isinstance(option_value, list):
                if len(option_value) > 0:
                    options_dict[option] = f'--disable-features={",".join(option_value)}' + ' ^'
                else:
                    options_dict[option] = ''

        try:
            options = ProfileOptions(**options_dict)
            profiles[name] = Profile(
                name=name,
                urls=data['urls'],
                profile_dir=data['profile_dir'],
                options=options,
            )
        except KeyError as e:
            logger.error(f"Missing required field in profile {name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error processing profile {name}: {e}")
            raise

    return profiles

def generate_bat_content(profile: Profile) -> str:
    """Generate the content for a .bat file."""
    header = "#!/bin/bash\n/usr/bin/brave-browser ^"
    # --profile-directory="Profile 3" --no-first-run --disable-extensions
    commands = [ header
                , *profile.options.to_list() # Huom! *
                , f'--profile-directory="{profile.profile_dir}" ^'
                , '--new-window '
                f'"{profile.urls[0]}"']
                # , f'"{profile.urls[0]}"{delay}']
    # print(f'header: {header}')
    
    
    [commands.extend([
            f'{header}',
            f'--profile-directory="{profile.profile_dir}" ^',
            f'"{url}"\n']) for url in profile.urls[1:]]
    # print(f'commands: {commands}')

    if os.name == 'nt':
        # Windows
        return "\n".join(commands) + "\n" # Windows
    elif os.name == 'posix':
        # Linux
        return ("\n".join(commands) + "\n").replace('^', '\\') #Linux
    else:
        # Unsupported OS
        logger.error(f"Unsupported OS: {os.name}")
        return ""

def generate_bats(profiles: Dict[str, Profile], bat_folder: Path) -> None:
    """Generate .bat files for all profiles."""
    for profile in profiles.values():
        bat_content = generate_bat_content(profile)
        if profile.name != "Default":
            bat_file = bat_folder / f'{profile.name}.sh'
            try:
                # print(f'bat_file: {bat_file}')
                with open(bat_file, 'w', encoding='utf-8') as f:
                    f.write(bat_content)
                os.chmod(bat_file, 0o755)  # Make the file executable
                logger.info(f"Generated {bat_file}")
            except Exception as e:
                logger.error(f"\n\nError generating for profile {profile.name}, {bat_file}\nError: {e}\n{bat_content}\n")

def list_profiles(profiles: Dict[str, Profile]) -> None:
    """List all configured profiles."""
    logger.info("Configured profiles:")
    for name, profile in profiles.items():
        urls_str = ", ".join(profile.urls)
        logger.info(f"- {name}: {urls_str} ({profile.profile_dir})")


def folders_exist(paths) -> bool:
    print(get_all_paths(paths))
    """Check if all required directories exist."""
    for name, path in get_all_paths(paths).items():
        if name.endswith('_folder'):
            if path.is_dir():
                print(f"Directory: {name}: {path} exists")
                continue
            print(f"Directory: {name}: {path} does not exist")
            return False
        if name.endswith('_file_path'):
            if path.is_file():
                print(f"File: {name}: {path} exists")
                continue
            print(f"File: {name}: {path} does not exist")
            return False
        
    print("All required files and directories exist.")
    return True


def main(paths: BravePaths) -> None:
    """Main function to update profile names in both Local State and Preferences files."""
    # PathEnum.
    # if PathEnum file. is_file():
    #     print(f"PathEnumFile: {PathEnum.PathEnumFile} exists")
    # else:
    #     print(f"PathEnumFile: {PathEnum.PathEnumFile} does not exist")
    #     # print("Generating PathEnumFile...")
    #     # init_dirs()
    #     # generate_enum_file(enum_name='PathEnum', values=PathEnum.__dict__)
    #     # print(f"PathEnumFile: {PathEnum.PathEnumFile} generated")
    if not folders_exist(paths):
        print("Required directories do not exist. Exiting.")
        return
            
    # exit()
    # bat_folder = make_folder_with_gitkeep(BASE_DIR / "bat")
    profiles = load_config(paths)
    # print(profiles)
    # exit()
    # Set profile avatars
    # set_profile_avatar(profiles)
    # get_favicons_from_web(profiles)
    # Generate .bat files
    # exit()
    generate_bats(profiles, paths.batchs_folder)

    # exit()
    # # List profiles
    # try:
    #     list_profiles(profiles)
    # except Exception as e:
    #     logger.error(f"Failed to list profiles: {e}")

    # Update profile names
    view_only = True
    view_only = False
    # profile_mapping = {v.profile_dir: v.name for v in profiles.values()}
    # print(f'profile_mapping: {profile_mapping}\n')
    if os.geteuid() != 0:
        print("Consider running with sudo if needed to access all processes.")
    kill_brave()
    # exit()
    if not wait_for_brave_shutdown():
        sys.exit("Brave did not close in time. Please close it manually.")
    print("You can now safely edit Preferences.")
    
    print('local state update:')
    local_state_dict = force_update_local_state(profile_dict=profiles, view_only=view_only, paths=paths)
    print('end local state update:')
    print(f'local_state_dict: {local_state_dict}')
    # exit()
    print('\npreferences update:')
    prefs_dict = update_profile_name_in_dir_preferences(profile_dict=profiles, view_only=view_only, paths=paths)
    print('end preferences update:')
    print(f'prefs_dict: {prefs_dict}')
    
    if not set(local_state_dict.keys()) == set(prefs_dict.keys()):
        logger.error("Keys in Local State and Preferences do not match")
        return

    for key, local_value in local_state_dict.items():
        if local_value == prefs_dict[key]:
            logger.info(f'Directory: {key!r:10}, at Local state: {local_value!r:10}, at Preferences: {prefs_dict[key]!r:10}')
        else:
            logger.warning(f'Directory: {key!r:10}, at Local state: {local_value!r:10}, at Preferences: {prefs_dict[key]!r:10}, Did you run all batch files?')
            
if __name__ == '__main__':
    main(BravePaths())


# git remote add origin https://github.com/poutila/brave_profiles.git
# git branch -m main
# git push -u origin main