from dataclasses import dataclass, field
from typing import List
from pathlib import Path
import xml.etree.ElementTree as ET

# from brave_profiles_main.PathEnum import PathEnum
from brave_profiles_main.config import BravePaths
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

BASE_DIR = Path(__file__).resolve().parent


@dataclass
class ProfileOptions:
    """Configuration options for a Brave profile."""

    start_maximized: str = field(default="--start-maximized")
    devtools: str = field(default="")
    no_default_browser_check: str = field(default="--no-default-browser-check")
    incognito: str = field(default="")
    disable_features: List = field(default_factory=list)

    def to_list(self):
        return [
            x
            for x in [
                self.start_maximized,
                self.devtools,
                self.no_default_browser_check,
                self.incognito,
                self.disable_features,
            ]
            if x != ""
        ]


@dataclass
class Profile:
    """Represents a Brave browser profile configuration."""

    name: str = field(default="Default")
    urls: List[str] = field(default_factory=list)
    profile_dir: str = field(default="Default")
    options: ProfileOptions = field(default_factory=ProfileOptions)
    # profile_avatar_source_dir: Path = field(default_factory=Path, init=False)
    avatar_icon: str = field(default="chrome://theme/IDR_PROFILE_AVATAR_26", init=False)
    png_source_file_path: Path = field(default_factory=Path, init=False)
    png_new_name: str = field(default="avatar_edgy_rewards.png", init=False)
    cwd: Path = field(default_factory=Path.cwd)
    # profile_avatar_source_dir : Path = field(default_factory=lambda: BASE_DIR / "Avatars", init=False)
    profile_avatars_source_dir: Path = field(
        default_factory=lambda: BravePaths().avatars_local_folder, init=False
    )
    paths: BravePaths = field(default_factory=BravePaths, init=False)

    def __post_init__(self):
        if self.options is None:
            self.options = ProfileOptions()
        if self.profile_avatars_source_dir.is_dir():
            if self.profile_dir == "Default":
                print("Also default is in.")
                # self.png_new_name = avatar_edgy_rewards.png
                # self.avatar_icon = "chrome://theme/" + self.avatar_icon
                # print('Not processing default profile.')
                return
            tree = ET.parse(self.paths.avatar_list_file_path)  # joka kerta!
            root = tree.getroot()
            avatar_name_vs_file_name = {}
            for child in root:
                avatar_label = child.attrib.get("name")
                # print(f"Label: {avatar_label}")
                png_file_name = child.attrib.get("file").split("/")[-1]
                # print(f"File name: {png_file_name}")
                avatar_name_vs_file_name[avatar_label] = png_file_name
                # print(f"Profile: {self.profile_dir}")

                # print(f"{avatar_label[:-2]}{int(self.profile_dir.split()[-1]) + 55}")
            avatar_found = False
            self.avatar_icon = (
                f"{avatar_label[:-2]}{int(self.profile_dir.split()[-1]) + 55}"
            )
            for item in self.profile_avatars_source_dir.glob("*.png"):
                if self.name == item.stem:
                    self.png_source_file_path = item
                    avatar_found = True
                    break
            if not avatar_found:
                logger.error(
                    f"Avatar for profile {self.name} not found in {self.profile_avatars_source_dir}."
                )
                exit(1)

            self.png_new_name = avatar_name_vs_file_name[self.avatar_icon]
            self.avatar_icon = "chrome://theme/" + self.avatar_icon


if __name__ == "__main__":
    tree = ET.parse(BASE_DIR / "brave_avatar_list.xml")
    root = tree.getroot()
    print(f"Root tag: {root}")
    avatar_label_file_name_mapping = {}
    for child in root:
        print(f"Child tag: {child.tag}, attributes: {child.attrib}")
        avatar_label = child.attrib.get("name")
        png_file_name = child.attrib.get("file").split("/")[-1]
        avatar_label_file_name_mapping[avatar_label] = png_file_name

    print(f"Avatar label to file name mapping: {avatar_label_file_name_mapping}")
    # exit()
    profile = Profile(
        name="ampparit",
        urls=["https://example.com"],
        profile_dir="Profile 1",
        options=ProfileOptions(),
    )
    print(f"Profile name: {profile.name!r}")
    print(f"Profile directory: {profile.profile_dir!r}")
    print(f"Profile avatar source directory: {profile.profile_avatars_source_dir!r}")
    print(f"Profile avatar source file path: {profile.png_source_file_path!r}")
    print(f"Profile avatar new name: {profile.png_new_name!r}")
    print(f"Profile IDR: {profile.avatar_icon!r}")
    print(f"Current working directory: {profile.cwd!r}")
    exit()
    # Example usage
