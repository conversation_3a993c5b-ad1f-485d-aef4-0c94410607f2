import shutil
from pathlib import Path
from typing import Optional, Union
from dotenv import dotenv_values
import logging
import os
from time import sleep
import inspect

BASE_DIR = Path(__file__).resolve().parent
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__) 

class BravePaths:

    def brave_local_state_file_path(self) -> Path:
        # BRAVE_LOCAL_STATE_FILE_PATH
        return self.brave_user_folder()  /"Local State"
    
    def brave_avatar_dest_folder(self) -> Path:
        # BRAVE_AVATAR_DEST_FOLDER
        return self.brave_user_folder()  /"Avatars"
        
    @staticmethod
    def brave_exe_file_path() -> Path:
        # BRAVE_EXE_FILE_PATH
        if os.name == 'nt':
            # Windows
            # return Path(r"C:/Users/<USER>/AppData/Local/BraveSoftware/Brave-Browser/User Data")
            return Path(r"C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe")
        elif os.name == 'posix':
            path = Path("/usr/bin/brave")
            if path.exists():
                return path
            else:
                # Check for alternative paths
                alternative_paths = [
                    Path("/usr/bin/brave-browser"),
                    Path("/usr/local/bin/brave"),
                    Path("/usr/local/bin/brave-browser")
                ]
                for alt_path in alternative_paths:
                    if alt_path.exists():
                        return alt_path
                    
                logger.error("Brave executable not found in standard locations.")
        
        # Unsupported OS
        logger.error(f"Unsupported OS: {os.name}")
        return Path("")
        
    def brave_user_folder(self) -> Path:
        # BRAVE_USER_FOLDER
        if os.name == 'nt':
            # Windows
            return Path(r"C:/Users/<USER>/AppData/Local/BraveSoftware/Brave-Browser/User Data")
        elif os.name == 'posix':
            # Linux
            return Path.home() / ".config/BraveSoftware/Brave-Browser"
        else:
            # Unsupported OS
            print(f"Unsupported OS: {os.name}")
        return Path("")
    

def copy_file(source_path: Path, dest_dir: Path, new_name: Optional[str] = None, message: Optional[bool] = False) -> bool:
    """
    Create a backup of a file with proper error handling.
    
    Args:
        source_path: Path to the source file to backup
        backup_dir: Directory where the backup should be stored
        backup_name: Optional name for the backup file. If not provided, uses original filename
        
    Returns:
        bool: True if backup was successful, False if failed
    """
    try:
        # Ensure backup directory exists
        dest_dir.mkdir(parents=True, exist_ok=True)
        
        # Determine backup filename
        if new_name is None:
            new_name = source_path.name
            
        backup_path = dest_dir / new_name
        
        # Create backup
        shutil.copy2(source_path, backup_path)
            
        if backup_path.is_file():
            if message:
                print(f"🧾 {source_path} copied to: {backup_path}")
            return True
        
    except Exception as e:
        print(f"Error in copying: source: {source_path!r}, dest_dir: {dest_dir!r}, new_name: {new_name!r}\n{e}")
        return False

def make_folder_with_gitkeep(folder: Union[str,Path] = '', message: Optional[bool] = False, keep_gitkeep: Optional[bool] = True) -> Path:
    """
    Create a folder in the current directory.
    
    Args:
        folder: Name of the folder to create
        
    Returns:
        Path: Path object for the created folder
    """
    if isinstance(folder, Path):
        folder_path = folder
    elif isinstance(folder, str):
        if len(folder) == 0:
            folder = "new_folder"
            message = True
        folder_path = Path().cwd() / folder
    else:
        raise TypeError(f"Expected str or Path, got {type(folder)}")
    # If no folder name is provided, use a default name

    try:
        folder_path.mkdir(parents=True, exist_ok=True)
        if keep_gitkeep:
            git_keep_path = folder_path / ".gitkeep"
            git_keep_path.touch(exist_ok=True)
            if git_keep_path.is_file():
                if message:
                    print(f"✅ Created folder: {folder_path} with .gitkeep file")
                return folder_path
            else:
                print(f"⚠️ Error creating .gitkeep in {folder_path}")
                raise FileNotFoundError(f"Failed to create .gitkeep in {folder_path}")
    
    except Exception as e:
        print(f"Error creating folder {folder}: {e}")
        raise
    if folder_path.is_dir():
        if message:
            print(f"✅ Created folder: {folder_path}")
        return folder_path
    else:
        print(f"⚠️ Error creating folder {folder_path}")
        raise FileNotFoundError(f"Failed to create folder {folder_path}")

def check_if_folder_exists_and_create(folder: Path, keep_gitkeep: Optional[bool] = False) -> bool:
    """
    Check if a folder exists, and create it if it doesn't.
    
    Args:
        folder: Name of the folder to check/create
        
    Returns: true if folder exists, false if not"""
    print(f"check_if_folder_exists_and_create: {folder}")
    counter = 0
    if isinstance(folder, Path):
        while not folder.is_dir():
            print('while loop')
            make_folder_with_gitkeep(folder=folder, message=True, keep_gitkeep=keep_gitkeep) 
            counter += 1
            sleep(1)
            if counter > 10:
                print(f"⚠️ Destination folder can not be created: {v}")
                return False
    return True

def init_dirs():
    folder_name_vs_path: dict[str, Path] = {}
    brave_paths = BravePaths()
    enum_name = ''
    # brave_oriented = {}
    # for k, v in dotenv_values(dotenv_path=BASE_DIR.parent / '.env').items():
    #     if k. startswith('BRAVE_'):
    #         brave_oriented[k] = v
    #         continue
    # print(f"brave_oriented: {list(brave_oriented.keys())}")
    # def is_method(member):
    #     return inspect.isfunction(member) or inspect.ismethod(member)
    # methods = [method[0] for method in inspect.getmembers(brave_paths, predicate=is_method)]
    # print(f"methods: {methods}")
    # results = [getattr(brave_paths, method.lower())() for method in list(brave_oriented.keys()) if not method.find('LIST') > -1]
    # print(f"results: {results}")
    # zipped = zip(list(brave_oriented.keys()), results)
    # for k, v in zipped:
    #     print(f"key: {k}, value: {v}")

    # # exit()
    def return_exists(key: str = '', exists: bool = False, name_vs_path: dict = {}, path: Path = Path()) -> str:
        if exists:
            name_vs_path[key] = path
            return f'{key}: {path} exists'
        return f'⚠️ {key}: {path} does not exist.'
    
    for k, v in dotenv_values(dotenv_path=BASE_DIR.parent / '.env').items():
        print(f"\nkey: {k}, value: {v}")
        if v == 'create_in_scripts_folder':
            v = BASE_DIR / '_'.join(k.split('_')[:-1]).lower()
            if check_if_folder_exists_and_create(folder=v, keep_gitkeep=True):
                print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                # folder_name_vs_path[k] = path
                continue
            print(return_exists(key=k))
            exit()

        elif v == 'check' and not k.startswith('BRAVE_'):
            if str(k) == 'AVATARS_LOCAL_FOLDER':
                v = BASE_DIR / k.split('_')[0].lower().capitalize()
                if v.is_dir():
                    # folder_name_vs_path[k] = v
                    print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                    continue
                print(return_exists(key=k))
                exit()

            elif str(k) == 'AVATAR_LIST_FILE_PATH':
                v = BASE_DIR / 'brave_avatar_list.xml'
                if v.is_file():
                    print(v)
                    # folder_name_vs_path[k] = v
                    print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                    continue
                print(return_exists(key=k))
                exit()

            elif str(k) == 'PROFILES_JSON_FILE':
                v = BASE_DIR / 'brave_profiles.json'
                if v.is_file():
                    print(v)
                    # folder_name_vs_path[k] = v
                    print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                    continue
                print(return_exists(key=k))
                exit()

            elif str(k) == 'PROFILES_BOOLEANS_FILE':
                v = BASE_DIR / 'brave_start_options.json'
                if v.is_file():
                    print(v)
                    # folder_name_vs_path[k] = v
                    print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                    continue
                print(return_exists(key=k))
                exit()
                

        if str(k).startswith('BRAVE'):
            if v == 'check':
                v = getattr(brave_paths, k.lower())()
                if k.find('FILE') > -1:
                    if v.is_file():
                        print(v)
                        # folder_name_vs_path[k] = path
                        print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                        continue
                    print(return_exists(key=k))
                    exit()
                elif k.find('FOLDER') > -1:
                    if v.is_dir():
                        print(v)
                        # folder_name_vs_path[k] = path
                        print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                        continue
                print(return_exists(key=k))
                exit()

            elif v == 'create_in_brave_user_folder':
                v = getattr(brave_paths, k.lower())()
                print(v)
                if check_if_folder_exists_and_create(folder=v, keep_gitkeep=False):
                    # folder_name_vs_path[k] = path
                    print(return_exists(key=k, exists=True, name_vs_path=folder_name_vs_path, path=v))
                continue
            print(return_exists(key=k))
            exit()

        elif str(k) == 'ENUM_NAME':
            enum_name = v
            continue

    return folder_name_vs_path
    

def generate_enum_file(enum_name: str = 'PathEnum', values: dict[str, Path] = {}):
    write_path = BASE_DIR / f'{enum_name}.py'
    lines = [
        f"from enum import Enum\nfrom pathlib import Path\n\nclass {enum_name}(Enum):"
    ]
    print('-----------------------')
    lines.extend([f"    {key} = Path(r'{value}')" for key, value in values.items()])
    
    file_content = "\n".join(lines) + "\n\n"
    file_content += f"    @staticmethod\n"
    file_content += f"    def this_file() -> Path:\n"
    file_content += f"        return Path(__file__)\n\n"
    file_content += "if __name__ == '__main__':\n"
    file_content += f"    for item in {enum_name}:\n"
    file_content += "        print(f'item.name: {item.name} item.value: {item.value}')\n"
    file_content += f"    print(f\'{enum_name}.this_file(): {{{enum_name}.this_file()}}\')\n"
    # print(file_content)
    write_path.write_text(file_content)
    print(f"✅ Enum '{enum_name}' written to {write_path}")

def generate_config_py(paths: dict[str, Path], file_name: str = "config.py"):
    lines = [
        "from pathlib import Path",
        "from dataclasses import dataclass\n",
        "@dataclass",
        "class BravePaths:",
        "    root: Path = Path.home()",
        "    base_dir: Path = Path(__file__).resolve().parent\n",
    ]

    for key, value in paths.items():
        prop_name = key.lower()
        path_str = f"Path(r'{value}')"
        lines.append(f"    @property")
        lines.append(f"    def {prop_name}(self) -> Path:")
        lines.append(f"        return {path_str}\n")

    lines.append("    @staticmethod")
    lines.append("    def this_file() -> Path:")
    lines.append("        return Path(__file__)\n")

    config_file_path = BASE_DIR / file_name
    config_file_path.write_text("\n".join(lines))
    print(f"✅ Config file written to {config_file_path}")


def main():
    # Example usage
    source_path = Path("example.txt")
    dest_dir = Path("backup")
    new_name = "example_backup.txt"
    return


if __name__ == "__main__":
    # main()
    # init_dirs()
    generate_config_py(init_dirs())
    pass