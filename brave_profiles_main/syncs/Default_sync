{"apps": true, "autofill": true, "bookmarks": true, "cached_passphrase_type": 4, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": true, "apps": true, "arc_package": false, "autofill": true, "autofill_profiles": true, "autofill_valuable": false, "autofill_wallet": true, "autofill_wallet_credential": true, "autofill_wallet_metadata": true, "autofill_wallet_offer": true, "autofill_wallet_usage": true, "bookmarks": true, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": true, "dictionary": true, "extension_settings": true, "extensions": true, "history": true, "history_delete_directives": true, "incoming_password_sharing_invitation": true, "managed_user_settings": false, "nigori": true, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": true, "passwords": true, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": true, "printers": false, "printers_authorization_servers": false, "priority_preferences": true, "product_comparison": false, "reading_list": true, "saved_tab_group": true, "search_engines": true, "security_events": true, "send_tab_to_self": true, "sessions": true, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": true, "themes": true, "user_consent": true, "user_events": false, "web_apps": true, "webapks": false, "webauthn_credential": true, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": "djExUnI2MXolwyroKKOnOaw6Td8hBzJc/IXoCXYyhRskec2yIyF//zh+yUjCjNZOPCeGyhyPUV1y8gGAvVDR5TgFZA=="}, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "feature_status_for_sync_to_signin": 3, "first_full_sync_completed": true, "gaia_id": "B70E8635E7869BDEB3C83878E0E12737FB58703303C08C76F617C2182B55509B", "has_setup_completed": true, "keep_everything_synced": true, "local_data_out_of_sync": false, "local_device_guids_with_timestamp": [{"cache_guid": "RH5c1d7Lva7a8OjcGqvPZg==", "timestamp": 155014}], "passwords": true, "passwords_per_account_pref_migration_done": true, "payments": true, "preferences": true, "reading_list": true, "saved_tab_groups": true, "tabs": true, "themes": true, "transport_data_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": {"sync.bag_of_chips": "", "sync.birthday": "1", "sync.cache_guid": "RH5c1d7Lva7a8OjcGqvPZg==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "********"}}, "typed_urls": true}