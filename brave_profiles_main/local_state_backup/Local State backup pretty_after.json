{"autofill": {"ablation_seed": "1DQu/xN/S0U="}, "brave": {"ad_block": {"checked_all_default_regions": true}, "ai_chat": {"p3a_last_premium_check": "13393156323690008", "p3a_last_premium_status": false, "p3a_omnibox_autocomplete": [{"day": 1748466000.0, "value": 5.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 5.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 0.0}, {"day": 1748034000.0, "value": 2.0}]}, "brave_ads": {"enabled_last_profile": false, "p3a": {"ntp_event_count": {}, "ntp_event_count_constellation": {}, "ntp_known_campaigns": {"42ce5502-614c-452c-ae8b-6e3a15ea8e25": {"expiry_time": "13395394787575425", "viewed": true}, "4de0190b-0feb-4e60-afdc-13fd5b0f185d": {"expiry_time": "13395223124195735", "viewed": true}, "50eaae84-2c4e-4942-b009-6634ae8ffad0": {"expiry_time": "13395747232091484"}, "60261cfb-c5f5-4353-a29d-ccca18d646e3": {"expiry_time": "13395160052508797"}, "d0667bd6-f0f8-48f5-95f4-45fd32479669": {"expiry_time": "13395575250939550", "viewed": true}, "f0d99c99-9f29-496b-a9fe-0ac9fa1ef1f6": {"expiry_time": "13395394787575442"}}}}, "core_metrics": {"pages_loaded": [{"day": 1748638800.0, "value": 9.0}, {"day": 1748552400.0, "value": 12.0}, {"day": 1748466000.0, "value": 73.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 57.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 55.0}], "pages_reloaded": [{"day": 1748552400.0, "value": 1.0}, {"day": 1748466000.0, "value": 2.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 0.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 3.0}, {"day": 1748034000.0, "value": 1.0}]}, "enable_search_suggestions_by_default": true, "misc_metrics": {"browser_usage": [{"day": 1748638800.0, "value": 1.0}, {"day": 1748552400.0, "value": 1.0}, {"day": 1748466000.0, "value": 1.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 1.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 1.0}, {"day": 1748034000.0, "value": 1.0}], "failed_https_upgrade_metric_added_time": "13392507600000000", "last_doh_fallback": 0, "menu_group_actions": {"browser_views": 17.0}, "menu_shown_storage": [{"day": 1748034000.0, "value": 17.0}], "total_dns_requests": [{"day": 1748638800.0, "value": 698.0}, {"day": 1748552400.0, "value": 1196.0}, {"day": 1748466000.0, "value": 8519.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 1260.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 4348.0}], "uptime_frame_start_time_v2": "13393154224979167", "uptime_sum": "690387330", "weekly_uptime_storage": [{"day": 1748638800.0, "value": 702.0}, {"day": 1748552400.0, "value": 5460.0}, {"day": 1748466000.0, "value": 42849.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 3390.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 26344.0}]}, "new_tab_page": {"customize_p3a_usage": 2, "p3a_new_tabs_created": [{"day": 1748638800.0, "value": 1.0}, {"day": 1748552400.0, "value": 0.0}, {"day": 1748466000.0, "value": 8.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 3.0}, {"day": 1748206800.0, "value": 0.0}, {"day": 1748120400.0, "value": 1.0}], "p3a_sponsored_new_tabs_created": [{"day": 1748466000.0, "value": 2.0}, {"day": 1748379600.0, "value": 0.0}, {"day": 1748293200.0, "value": 2.0}]}, "onboarding": {"last_shields_icon_highlighted_time": "13392568063135148"}, "p3a": {"notice_acknowledged": true, "randomness_meta": {"express": {"current_epoch": 249, "current_pk": "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", "next_epoch_time": "13393209600000000"}, "slow": {"current_epoch": 24, "current_pk": "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", "next_epoch_time": "13393209600000000"}, "typical": {"current_epoch": 108, "current_pk": "/vfFXuBfk90px04bxknpuiaLobvNzsa9qYeZ5o6KdEgAAQAAAAAAAAC2HxKfwjSAsiX74zDYzKoNwUWaOo93LH1c/wMv/sVvGwFsSrLA0DhZerIsroynxv52BOiQcz42079hUQinjIupSQKO07cywNgPlwMI05KPuMvv52jP1ftGAHceWK+tjgWSGgNePWZrZVuYBvkq47Hi1oBcmrqu7ZdLyctsPODp8dFfBwT6Gud0yVwGitRs0qpzqAkU6A7LWzvk36PBhty3T+4rKgVkmQ5TjEOOuxD6Fa3fv4C4MII4VL1ZSxzR+aUx/djQRQaIRMKj9z8DTuhj/M3QhfcPisezkE10iZT0Xl+gtWKqEQdOo3ftMyOKqHIfe3ewxRwMm/+uCNu3jV26pJLhZPYMaAjgWRksSMTISqyRX1gdWu6VLmH4PTilO7KLUW+tl9s6RwkaEfy5vnfrrAYDeGDAeiNNWWR/wG8T3cTK4tABxppBEAroMBP7kZbvLZCrfxUdeAFgSFefGYN4rtPrgpDRNeciIQs66D+YMbKWZW+WGm2mQUYJ+Nf5UvgdoT/HuhOgADerNAw0cuS5pf6SyHy7VrHHw2vhpEEW6TtLHLD+yKiPGxLfZg08hXEOJj7cPBi0w2HvqGdA0G8p0eivLLjmyRNx9iY9Vg48pPCiGhxnHvFxXZ0YaMSKH48+2NuRxTGqMWQqoPq8dQ+cjrzT0OaYMV1lhrkFZ2nQiM8S0hQA+E5nxKjRzgmvMhDySqx5EXlpZgUwhziElJq97ivrIXQLk93SgCInNO2lIxGElkbSSkgGECVrPEBFz3vOaxzSCmIoG7ZDxFj2s0IbMBIcltG34STdrYp39WqsaJpHvezS994+1wy5TunOx81oDBMutyoQsRU2cX1SpKhtoKerZO0GtQydQMzGoCoMVtr3YhTamJaYTf4KaSAE7De3Ubp+hacP8RjIRPXTQak+JEUEWxXENsaOrBUkMPxfNGfn6XdGHYOgN6VJRGgjZ3WExr4rNBYY0E7v+r9Z0bdyq/qqLDXPPjyI8ZGibBWbl2d4wpVUDRfkiR1hapn8uQMhbcjZxsCZUz1HitnGPmrlOEaSyIb9XBjUCmfJZ5hkmuf9smnUmWlQK6fO/qu7351YOSiRsX68RBm4+vBGqlWEmn1VSldCjj9NCHGv1YmuCNS25JHoBz+MHRr2UrDciPxOr4Fh6kgqmQSz6yLavk3y833rrBLox0EZPRvAN3BOq3XVbcHaBMztRKJPsF0IQvmngWFevaIYu7+pLBziLDnWVZBIkZHSqTOS7OYADU6VGq7go5EcCbkobM1gQB0kp/RKAqV36S3Ovzj+kuarBC6r96vGr7SMBN0rUsJiZx4atmtMQLFUNIUhr/p77FThzZ9OwclwBz7UnZySfRqJXh8gHqdpSgmxdu58Su8TMkst+Ere6QH6UIxIoQJLsPz4ViBC0LHf91sHtkKiX2tSokM70T1rM2zIOgi2AIjbEipyKCEcpzggfEfoO9zhCE+q4URhwDYca/v4t7FxXj42heCNEiIUMNRr4hTy2Ry+gZ97RkUnoXu36uX9x7Z7pF2IETX4FiOgrfIF5hr3ij+/yTqUnMEfMUd0INOO12mzV2kb/UOgQiQIUhopFbT9X3EKvOJVfjnqxbMeMEQ7mZqKxqWZReagDCUWs4n8js4Ve0oCtWqGp0YhXlBLbsRACHLVZ7mxf7b4WybQrmXUBGsntJ2HYJyzUg8BOSAujImI8b/XA+7E4WGyKSe6K0WtrNobJ1i7FIqiFlmpJBHPs/dj357Iwz4RapkCdygMx8C7DDWmXvlBTLN6OhWFFtokCk7wjTjAbxwuFmnLKikW6jlY08MRXe5cYuGrVEozBiO+ZRv3K8Q3mU8pIDC5TypSGskHM/kZDTPspZXeHGMvL0AsJSTN1fnEHNJhSSZNJivGVPOIPIYwDz8JLevR6gKM0GxRGu8ov+BKFfHf3pruQizgBn4MDkG2F5yLo3pID9Fbwlh5l0JHqeuSupkMf+3EIS3GHdNZ7modcV9/iqgJNm8en5ckpD7c9/bxbZHyvtimSS4Wc/1mVeZb7Oj5FndK9cNRiNa17VynLmdJMPDCv2tfIi/kpWrBgHjpy2nwrJPVdtmVNVcUai7zTnmJV/Sf9+SnAjA0iZsNzL+IDLspVWbTCBi/Z9uW6FMSwTIW+Sfi+AvGaTFcUyiMKhkFTJ5WUejf1St7bdOGwEphETsfwpI+Z/7HXTI6vwRhzNIO3Y5Ke+afkDbxjd2VC9ZP4XRwMiy5UpmfUDMYRLyQjv5V5shXfM/SMDqklpVQ1X0ivyciOoRax5RgRjQOn3jBDz4WuMJFb0AWEQUQZWqmERxeQTmG57IOveMeNzVGVnIaiYIA8CPIn5JQrawao6q6p/guY4vO/P1GZIA+LjYI16XjjXmUcqSqLAV73tZpBkEcyNtZTDOlgCKQlQiCUjfq3Aon/MqQRn1/elTV35WgHgte3fkF7Wpx7c0YR+q0IDiemoZKaAot/IZrF7ziHmkw2rI8GAA6FZFP+nqbv8c3bzkgLH+/0KqJfa7BHoTWXkV/aMyPNOKusdTBvKI8jRs4VDpyVxcQuxD2FOQiqjWPG5V1BH29Djf2Iqi0Vd4/U6eqHTuIEBT83uK2+buGZltwtCzv87WHcwUMarsFybnF/SCgITxi8++oBgsuuq1+QIVh5+akKK38nDkC/YMJqkoZCwRkGT3yQuUuGeeCD0waTHTW0CpeSt7v54upstYyaU+Et/91Yz6QPDH0kMFdAIiqh0tfJDkcFEGTPCMEir4lYl2YgkyVKz90t6LOIuxwqvoyByLtLhyJYX46KiyGhd+/E9FR+vmUUkAsGXL1R2wChd2cPUsh/PU/jfYjmDwHEzOazNiRHFGmbUEOfRf+fQ0TOfn80NKvC+DWabWejBnwOeTN9Gm7n28uXELIWILd14velHe+iI0fCbwlCw9BM5/CYMImwyuaVsjBZEN8KgX/2HsPnWQcXx27wdzVXmTfvN6+jNjZ6NoUAOSrf0ScHcmgGuVlO1PfahJmm8rIHN0UU9/AxBqoOSI8uQa5REUipYDt3G945VU5TTsVKJiBojMzb8LlbiXSh+z/1XyLY0ao4un4wiWGuV/f5ZOeWbVRLdv2RVf1Hv66DWFdh55eH0eo+r8qP35i+bbOhwpHMoVDWMneASpMCH28afHV/YH2YEhKMXlXTOU4lSHmA+5HFtz7mgSJUhoyVpxNqHOEGYcwWkmU8CoiHF9NYO7NCW7dio6srgduxMV4IIv81VKeR2H8Y0qOR/9Qj4C4a7NJA0mMsmlRrlbQOXONBQQyZQtAQ9owGEvALcfWp1PJ4v3R0u/3BrVZr8pNb9028emcKPreL0FZTkwOnuO+Oq5ARiv2U/uxoA6XsIUQaalXJ29fKLamXSmIY01aN8INtMbyE1z3EKP2L2ZvyFWgLuNLDQacCEbdyOJgKE6meaggQeeUyFK2rxsBz+oIh7zVgsHLMp3lSns6XCY6T08Gcu6fW+Ht0DSNbVaa/wkgTgSZ1+NiYFNAW0NsNT5DE1Dsx/lnf/aFeF2ukg+d1eI7MUfWGnbCvoaeNSq239wcUlEmnbBJ01KnOrMnbsxPanSvHjaMubKXIrPms8uplKdoa1J26LASiidUd9FBRql8ZYSbH/EtsCOPpynt5kV8wpTRAFNApFW1bWioPb/p2xTfVYamlJfGAfhq7sfP0bcYR7uqWlQM1VH1aZgiOkCdFIR8BnDkjr0CN/F6O9SSXHX+4IOEH1V40F54uB1BfPeNtIzvOHN0FzJBDR9Psgj6ZDndt4LHNlZyFdceUrCdhhpbsTBXve+Q8dEGXf1Cb8/d/Mp0NTqaaFcWb7YJjtwhPtCd/ur6Zwf72ttK4/ZylmAx51GvtXCPDFgyHqWTmQDmjWVkbuvdGdvf18NGXhyFYjphF2sVjV8AAlnyB6nMrnJ7sNphHBpsvMVqIWevmXuu4tgheG89DI6wDlr0co1MuKD4VGXmmB0OzRGSyl7NSIzSgg+klb8hNJpGT1sodgrrN8nsl3Df/x2wDqESFLKeGwCWrMjrixF3Ubl4ZVxeP6tMqYV3XQS2IhW2IB7km78BBPAbqukCELA+c5M6e13+0EpNOAT3lqIqRy0aDfd2QxJ1i4BMF65Eb/QrCXqqd17ia31Fc9a+YoikaoI/PA/9ao5xJUiwjLpRcx8PFUjJAl8UOpgcK+BLRJ3+lssByTeb+uUYbKpeImmQw+vx2WJRRWAGHyV797xd9WxQv82d57K0zw2a1+7/bB2AMxNihvo5c2EaQMmOtRQX1VUeg6XiYGo4YZAjniQS1QVYSDiIB6yaJWL6HVb3cuFJ8QqeMjpvE3fh8PwKfJQXvI7zoOQZ98GcXmMurUD2bpKZKDiNa+w5WRCVdV6Y2yDM3UgpKLj+IMj3Q2RE7robAtFsPp8g8Xed9lP5RAYYmBjzwPTd/G5fhoVFCWWQYEd66ofV/mOYw32suNRZAzzCzGc/s6Ko2cMJdiVqOWZQgGjYpZ4jHMARDNa7FgikwReehsHdIbMZPSkb/zf5U2ecyqRORv34vARS8dbQpjIuvdDUBfAYAeXR0NunIxRqA2jKb84N7ZqB164iZKLLlM8vpbdWz/cajAmrYNzgabhmR2kGu5AMDNVPiUNTZLEeQ1MjNr0Dl0MlFCUiuuih/S7OHGrQI0+dUEQq+O5Fc4c+I/iObX5FqAbS7ESGz53Fr+INQWvSQcZAw9FLmr7YsnoNmavqhUxR+kJGYG4mTQSxVPLgNGwUqHKeyPOJ3UsuoLzMdLRqdwBrVkt0uDhnhq8Gwyamcm0OTXhJMmxEqfIw/LTrggG+srgeABBc8Jv6S8fd7kO8M24KcaT0Zvyk7ZzswIBjB+/uUq/jpTo6ko1UjJa0tj35CW9aRtqzAHYgHRNl+SwBNEc79tsKCpFiT1vZHPb9NzddDHDGFa+5A0km8LvTCcimb17MgEXI0jQ/1xDc1ttHjC3VcHEuO3pp4DGsU/oL7Gi1hZr9FZ6gBUfJR0LTCD4zop5vLnLItWz+CeQ2eKZ+5Vnlj6yIxwM1kpXPgaYZpa1H6/75Y3OsxzdJMB/nFhs0JENylYmP3COizHzW9UwDer8c4zyqT3SczwlHhgxQaz1JRr2oEMA19pSL9kykIImR5pERXzE0RXVGyPEE4UylcEXuhi5ZIUYfTTNzy0U4dc7KYUQoONjYRHas5s0TrrJnM0QghgPZNmGLWQCUfhAFibAiX+laQutKWXeQdYi8za7d5xSja4g97ZlQKDJeiqgVfpepLovWw61KS3g6byz3o9nzr5Te+t2WuxlIj3VgrjQ9IaEmIKMdNzehJHnuwhZVcP3vrgH7woDpfnOqvI4gJonVMIW2hP7fHFh/eno0ao/HVP757iK8D1EH663PulK6Jz1w0mUspOgyC3TrBnu+Qs5okTMadpPLDVvTtK5P+sFCmRUpjWa++s1RAIrXGnxGyrBr+Wx7Ymw6UDc1SkMgMfSZ0UitGL3+rEpkjkPmaX28p2hmN2j+X/7W3eiRWnWIeprxa3Vtjgjr1tjPxpbXVX76U1U7HdkyumtutoFjMPAD67bGNg3wy10uk0POpG8BOH8Ohp0l27zzCbQGP7nlhBX+tR04vm2spSj8y3q6DdCVIoC6Dxmx/JnwjXLrKEW55YSZcbg/oM6IREaN8PZaj+F5LIFI3ZYY1+iGh8EYO3p3o6I0FUYNgpWUVY2jXpzJgf4mJoJ0ouFqpafK7WidPw15HbDg7G2WphlfrVZmARSdZ7XmSoM8kYK23CURs87fB5nM5/I4uO3InAczy3nCgo3mrNddSIT+tS+MYZ6u1PnoF3t88ritDnKu6EL5cV+HXq8DRahAIYXGDevTd9CnWzSxvh16/S920HCUyTCj1z/cRf2+G2mdS4aWJXxWSe1cc3hiveX+Om75Ln2/cG9AWYvm9+morj1HFYfAcS3EHDat0Q/2lR5fLKZn3NUuEFuAQlwKxMBsMZq3FIhgHR3khKYvYPndqUexmcMMHuqT3xHYlSNrNFTieA+ODon0j4HW4NScCh6bk8TKXiLgNuIGo3PukkS8kCIXAHzTV4psdUp1MICMEjaPEi6bSZI3RewP22MV3Dgj22XvoZKDQotUEMR4IngB5rW3LcskZkA4SlZ6P39bgvZSajaiVWmmaYwinaiRDmX4aofpaeNIwmcw/avaTJRW7TzP0UkTC6mTJo2goVY+FDViAqT4/KFzMsbIb613lEDLAZV7/pYIN0WPfY6OhjgenYCrLNojmciQqoGTVPvHptGJREyxSuTonipPEI9OMcXhczBC3F6qKRnm+ip5VKcac0M3ig3lUhf1yPkZMJBoLK782daRjDDgyO4I1QPvX2VSpO8NuXBVbQmXZNXlOpF8zO94NIIpom/TImHe69pwsIuXbVKrf6VBfCRYI6kHG5JYDUKDjRHm3YCR6G9vz07Q5cSxCnAHJdXoVfwLa+OLbJMgkO7w3wS6xbp9RToy5+5j7BLbNsqTH7N7tJsC5BaAC5SIPYBSt3XIP4F2JdedOc02VkTw3K96fozpbcn/Im31MZVOlGLxu2YIF7OtXfMOAY6sdDRZsZAfGxJxVHd6rFNSIpb8fpnbZBtwDfJmvz7NKF11I7qeq3jLIw8CRAkkGVlWRZecRXZtNKWv+jbCiGaJBI2kRoAIoOI8F/GPNe8VlsQWU5iWeK55kmGiGEZJ/cKMstFEHfwZz8k+dB2oXYdZy0RMD5mYeW/hNOC1RJbt9wsyZCxzgIXNGKWDz9gF2uJNCIfYP5ooZzm872dm59E7Hs/yJVvrFYOrEFptEBF0JVqVFhN7BpsSQpGQvqL1D18MR1IA9YwCy0T2T449yQRgX6AqiSNSKZzs+q9KM4LdcEeNidNvCw2l+lZWKGhFZj8vEBoFxlmtUZ0q8b/3LCCfQ0Ue7KVCBT2MnkEFBndXXzqsIIHzGMrHOJ7gJBtsMlyKJzp2k8DYKd5Bp0bojy7nlh3zX0YOvOr8dZ+yXwkfKjbGPb8z+RvZR7OvRN9jYeaxZPCawjw5c3tnb6D8x914XKA00N9g4oNzfCinBYyP2JCXekudA8c/n5h2FKHEsjdEV2x6o8o9bAsTzQYIBM2rd7XO9qnmrKS56+aATqKGbVCrpOfpdrrYxjGmLMBUHQL3pg9ZDZrYJaRjrL2xPaPOOnT/aAZtE86bNPqGnpz8OvDoZttUbsUbSMah+lqeFqTUKd3rW6vhS71bjmSAXogLk/0Aacy7ks4moe3UF/WSCaXooCoF06ywbSvYSiAq8nf27ZWnjMFEzOsMlWZ24z9ad6aQF0oHl39Tv6ux/wnf+kI3JRAnJnE9q3JGS+pJJ+NVRadqjH4CHLPmrMDOznQCeo3okJjDVWBWM1djcLbxabo3bqg0vT3CIQs4ZewBJtSYh1t+v24Xx8F+rbB76tltCukJDqngYIVB5HgRc4q9te85e4WtPLN7BcQ12Ni4fFUt2qajHqqMmrDDcLcnnPS49C5yLa0TyM8YOLPdFTY3OR5rd5yaPKs2YPuKccrk1Qzwr32AuOGDxIC6UnExQ19MUqEDsgDlbqw2pU4IWJS6JNUhYHhM8By/J7XVHv1vjnyAnF74UsDVGK3eJxWhbKTwRAKhs3Nm8YWHQhaz14n8+f+T+/cuWJbwAK6mfKGTFuxlpwXLEA7uO5ZgZlq4SW3l6RnxAjyIVKMrTK+IM2gS/I+K4Pksn8u44DwgIjghxpZQnosG7h7uBVy7H7AOWBPSLr4+0/M6udiNNR/ezwUp9OUPqEOTyzSSeTXiOLGKdLi/ICZT0a0x/JSjfYEnkRNb4HMYuOgsMFeRu6qbe7JU9Hob5OfpSU7T91K89LVRWHveyvKce5oPp0l7mPSuP7No7Ij0kDM0LaNDsfMYSatj76VWQIisnATu7JFQEIYOCrRcc/YSyEfxvehrgaS2MTETk0qx+eJF9vb6hHEEEsjOJrX4H14rkwGxHD35/+kYjyEagxZxQL/SNDLbX8ll9OfCPbZ0q/VbhaycDFrgWAp3GP8CDL/yGfZ3ZPnTyXVpixcSDbdeZN/k1JcroaJ1+bhLW+NXX5k5Ht83dtjebL0lgOLsObjuJYWv8BykTb6Mdjvsl2cGnXgQ9XqJkiNWVHfmXkuZGbkUyzhiZv5f4ERb76boKle7GKQk2LHIsWtVfZSiJv5OPboGEtplv1HXUk1djiZUWXx1KHPD3mHNO8Vu3uokebRdT7saLH2FqQKkvMIIJiaxITFZJeEOX0Y9+E/lth8nNEvxALz4HWUwE9N62ioc10dUD1TnPSiPCVcnv5jqmoL9l+CALr1u6BxvUVbzccb9f/ZVTGqrfjCqbJHigJOus04u/sInD75wGt6E59F1N615yqQYqVuUeXjHxefF0+inbgFYoq3eCr9eo0iuEl6zOMIDUqX/shl3pLgko1IHqsKJn8n2Mjv0PMDufAp0wUomKEUg0ukUOwGpflkOGNjIQjarC00SI0DvcMEWN2CEbFGr8ePVS4o6ukw71KUiI3SvyB79/Z3I2XqUeMIayNU6H7dzysgzf1xwbEtzlu/YiKcqZEiyRtcp0SSqIsOiKEPhxkJoCCFHGFgOzbhKdHYs0fEGvJtfth2kzPV4ecRm6rvQA+pb2FQ15W4S47qNN69QA62u2zy1gBKiftf9GMXG1ZcQHkGfOXROUIkK+c084vwE3flvBpDrdD4U6vzcfMZ287uuaWjCkzcTvdF2FtS2yoyfDbqBpRhuJbW/8cV1fcdcjo7G98AkYv3pY1DhIymIgEH/WoXEAdzuB4uridHmFsiaFAtVw0FsAAxHPH9QZCbkNlSa2oJwwtUzzKQD3clbQMk2cbDqHCLciiEw68rTEpm3J54J9Oh+iFcObbbRNvBsAsrAoGrq43POZZrGG4SWEl+FoKmrfEN1GpNUIrv9kYlPBss6XdZKuyt5e64egYxb/WOAQ/bgFmJaJru+moCyDnlvCMxonVpYyIk5XFtWHyJc2f+tIM45+WVNDq+ZDT9LBukldc2Ydrh7aGfJCUFUWjsXOAzFO6j4wF7bctnYBikb31Wbes5kwBCL+0xpkGBKMSwE3NYX5jJv7nEUh6e4C6H5SAOxIs9mEY0BIv8MyssD899R3qDGNegTBhzTRspYhPOfuPi2OtCkD878iT274FAswV6mfrMiJzZ2kAk+219hFWnA+R6hEdFe6nzOlEh/xvOQfHzL2TLTVAqo19wX2J0oqXh6vGEaY9LwgcsHM2cG/RknXvUWrGdSfmgtQ+Cn8lRkCaCODAHHLdOWbY5Nd17c0eQL8pVww/qFdA92LFN7wwlDrUOBZj5SK9TcaMPIJd7UGpsMT4zrR4TXcOdMnfXPqS8WTzi3C2KTR9VwTUHBFyJoWzC/Vn6FAJLvYbaZWwtwEh+1OZtV1PIXFtbU6oPZ7UkYiSnXm1cAVYE3aDFK3gFdfF0093eEyyEWIteuZ+zddpwX5WOF0T9lmur9kmchb6mTvQV6KD4ie9alENjeKtCmEWsN0mvm4cjIjEU7P5wLBTAHFEYaV/CLorIFYNmCnFwFLwB++MQTbJNo/jDXst0lrMre1C5bJPYNXHa1RtpYc+g5FjA70vReC0Hfld2UCDxJWiikojP9gE6vPlvYUdtc2D1QMts8h/59qb7TI4bOFkF6eIKx5ThDQ6MpR4ZlCdy052xOnmTbwvFyEvJi0IWeF/Ml7f1mqgxJ50KZoJY4GN2ssrEOjlB6Ts6L1+suq9RXA7AYY0jJd0SuYdeV0UKmFN4o4Qm/n9HQO3bvxDvv/Qg03Qh3dc4R6TpE3DsZibtHcN8c7MMexWbKrkqIYIH2oux23iNe5n/b/sPw3/X6/LK6QODKMCk2Nd3hks16AmTFJuYV05iMHmNkY8TIWDa6CaS1JeEgR3xsI1cmrrcKN23cOB99himhctsu+zViqo6IiNPOGuKmM+qH9T3F71GWE8QGcJqt+V4u9pT6qxmFEUF61YPjMeOA3tuS7HXOHvRgloo30n+C1QmVdwC16oSIgTyVGcwWReQQ/Rs1/qsQn5ptwYot/7EnAIJDxDVq9rw+fVJMH9fsKeXkoWSLZBhpH9v0Lc1rY4MymmzhCyzqwMwsMiL+jI1XR+Y8r+3myIkylryJL+WHYvAvs5FlE9Yg6Lad48UdEgt6OOcqxh2SBMcZzNx9KOgsS9J+iDSrCe1CcpzXqDUuuYoKdOgeoDtp8vhifhlHMgH8I4H+eVZXDH8J4GwsBaUpoBxsP+muj64ufqoIUZYcw3a787P8Shz485+GH+UQBipWlV56O+q6YPKTG8HkDQ2zu4Zoh4ZAYubXA+c1OqS5fU9G3NKDeOsoUSHeVHBcOFkt76cin4Np6pwVsOsDsEnJtGzh4RaiKuw0PunMgUwO6Oh2V5V7Tfnj5gdhR49Q0jRk0jr6ZrdBC+1QzU6UeEkmgunRL9y/QS09qc5Jw+QNwkSJ6PD/t4KeFe48vEHi1qkhDspe7xvgrxsjf7kMEKyzqMthUOGzEnt6dO/CQAl1ZBn+aHWLcyadcdDtCkL/SLybHvhxp+qI4CiiFvDEhvbn6YRlK9BtzS1T59EkYf7nRlLhAvP5WSX60vCjTvFygH6fpp2cw0xJhfELpvWwBzTmDWL/WyhnxwZw8qSmfvIsiuySh2wrZW0+WErf26iUJc0xnlhdksL2A8gYEMcMWfMKHmGI0ZLEy8hey0cQvK0BPAl+5qxU1Hlwd2RDLz9sLPRynDeEOjH+CD+M4hD9wgdXpAbef7xa3CJ7id2AEDlXOvWSD9UGUPABXrLi2dZ1AUAloBEwVj6147WgZbUOdkxLafa0vRyQpge/GCMAJqAR9zuM6qHQTvR3IBrX9ST9J5OPZfceS8AMtepfUTZieEnUNCYszSCDOVMB8uvqDCPWyxkaP/i6K0WSJ2uYL0nqePDrLpgCcKH1fKwk6bgxDcvm89i1EvlcPwVLNz8qO2nT1cfrJYVfvGXSGmNSsgfXDtE7SAn9B/qQJNad6KquKzBL073U7yD28PsILAnyOY+FL3qrEOsocfuyQN1dIuyc3QMNdHClXA54WV5pu0K9w1hv7UTACID9IPzEXWVkE9WWQc+mbSSIal668iD8fEScXrTZrntl+i80MP3gLab/b+iKx0PiZIhsZw6T/Ev6cB3aTPqetiIFjB47BP4mwOivWfI57SVc6NUDyoseKdZ1+iHGYxFipYw0GKQTPf9KZbZOiczpAhGLJ9bFWSvy+mc8t02HenFYRqkiYfmQAg==", "next_epoch_time": "13393296000000000"}}}, "referral": {"initialization": true, "promo_code": "BRV001"}, "sidebar": {"target_user_for_sidebar_enabled_test": false}, "sns": {"resolve_method_migrated": true}, "stats": {"first_check_made": true, "last_check_month": 5, "last_check_woy": 22, "last_check_ymd": "2025-05-31", "week_of_installation": "2025-05-19"}, "wallet": {"wallet_report_unlock_time_ping": "0"}}, "brave_shields": {"p3a_usage": 0}, "breadcrumbs": {"enabled": false, "enabled_time": "13392568049074774"}, "browser": {"first_run_finished": true, "whats_new": {"enabled_order": ["PdfSearchify"]}}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "network_time": {"network_time_mapping": {"local": **********704.632, "network": **********000.0, "ticks": 124447186565.0, "uncertainty": 1102889.0}}, "optimization_guide": {"model_store_metadata": {}}, "os_crypt": {"portal": {"prev_desktop": "ubuntu:GNOME", "prev_init_success": true}}, "p3a": {"activation_dates": {}, "constellation_logs": {"107": {}, "108": {}}, "constellation_logs_express_v2": {"242": {}, "243": {}, "245": {}, "247": {}, "249": {}}, "constellation_logs_slow": {"24": {}}, "dynamic_metrics": {"campaignId.42ce5502-614c-452c-ae8b-6e3a15ea8e25.aware": 2, "campaignId.42ce5502-614c-452c-ae8b-6e3a15ea8e25.viewed": 2, "campaignId.4de0190b-0feb-4e60-afdc-13fd5b0f185d.aware": 2, "campaignId.4de0190b-0feb-4e60-afdc-13fd5b0f185d.viewed": 2, "campaignId.50eaae84-2c4e-4942-b009-6634ae8ffad0.aware": 2, "campaignId.60261cfb-c5f5-4353-a29d-ccca18d646e3.aware": 2, "campaignId.d0667bd6-f0f8-48f5-95f4-45fd32479669.aware": 2, "campaignId.d0667bd6-f0f8-48f5-95f4-45fd32479669.viewed": 2, "campaignId.f0d99c99-9f29-496b-a9fe-0ac9fa1ef1f6.aware": 2}, "last_constellation_rotation_timestamp": "13392801886631464", "last_express_constellation_rotation_timestamp": "13393154203575174", "last_express_rotation_timestamp": "13393154203573934", "last_rotation_timestamp": "13392801886631653", "last_slow_rotation_timestamp": "13392568049161002", "logs": {"Brave.AIChat.NewUserReturning": {"sent": false, "value": "0"}, "Brave.Core.CrashReportsEnabled": {"sent": false, "value": "0"}, "Brave.Core.FirstPageLoadTime": {"sent": false, "value": "0"}, "Brave.Core.NumberOfExtensions": {"sent": false, "value": "0"}, "Brave.Core.TabCount": {"sent": false, "value": "0"}, "Brave.Core.WeeklyUsage": {"sent": false, "value": "0"}, "Brave.Core.WeeklyUsage.Nebula": {"sent": false, "value": "0"}, "Brave.DNS.SecureSetting": {"sent": false, "value": "1"}, "Brave.Extensions.AdBlock": {"sent": false, "value": "0"}, "Brave.Extensions.SelectManifestV2": {"sent": false, "value": "0"}, "Brave.Importer.ImporterSource.2": {"sent": false, "value": "0"}, "Brave.NTP.CustomizeUsageStatus.2": {"sent": false, "value": "0"}, "Brave.NTP.DefaultPage": {"sent": false, "value": "0"}, "Brave.NTP.NewTabsCreated.3": {"sent": false, "value": "0"}, "Brave.NTP.SponsoredMediaType": {"sent": false, "value": "1"}, "Brave.Omnibox.SearchCount.NonRewards": {"sent": false, "value": "0"}, "Brave.P2A.new_tab_page_ad.opportunities": {"sent": false, "value": "1"}, "Brave.P3A.SentAnswersCount": {"sent": false, "value": "0"}, "Brave.Search.SwitchEngine": {"sent": false, "value": "0"}, "Brave.Search.WebDiscoveryAndAds": {"sent": false, "value": "0"}, "Brave.Search.WebDiscoveryEnabled": {"sent": false, "value": "0"}, "Brave.Shields.AdBlockSetting": {"sent": false, "value": "1"}, "Brave.Shields.CookieListEnabled": {"sent": false, "value": "1"}, "Brave.Shields.DomainAdsSettingsAboveGlobal": {"sent": false, "value": "0"}, "Brave.Shields.DomainAdsSettingsBelowGlobal": {"sent": false, "value": "0"}, "Brave.Shields.FingerprintBlockSetting": {"sent": false, "value": "1"}, "Brave.Shields.UsageStatus": {"sent": false, "value": "0"}, "Brave.Sync.Status.2": {"sent": false, "value": "0"}, "Brave.Today.NewUserReturning": {"sent": false, "value": "0"}, "Brave.Today.NonRewardsAdViews": {"sent": false, "value": "0"}, "Brave.Today.WeeklySessionCount": {"sent": false, "value": "0"}, "Brave.Today.WeeklyTotalCardViews": {"sent": false, "value": "0"}, "Brave.Wallet.NewUserReturning": {"sent": false, "value": "0"}}, "logs_constellation_prep": {"Brave.AIChat.NewUserReturning": {"sent": true, "timestamp": 1748329281.434559, "value": "0"}, "Brave.Core.BookmarkCount": {"sent": true, "timestamp": 1748330448.424086, "value": "0"}, "Brave.Core.CrashReportsEnabled": {"sent": true, "timestamp": 1748329191.195938, "value": "0"}, "Brave.Core.DomainsLoaded": {"sent": true, "timestamp": 1748330171.833506, "value": "1"}, "Brave.Core.FailedHTTPSUpgrades.2": {"sent": true, "timestamp": 1748330146.625961, "value": "0"}, "Brave.Core.NumberOfExtensions": {"sent": true, "timestamp": 1748328505.023533, "value": "0"}, "Brave.Core.PagesLoaded.NonRewards": {"sent": true, "timestamp": 1748330605.303949, "value": "4"}, "Brave.Core.PagesReloaded": {"sent": true, "timestamp": 1748330575.08107, "value": "1"}, "Brave.Core.TabCount": {"sent": true, "timestamp": 1748329598.343891, "value": "0"}, "Brave.Core.WeeklyUsage": {"sent": true, "timestamp": 1748328640.448329, "value": "2"}, "Brave.Core.WeeklyUsage.Nebula": {"sent": true, "timestamp": 1748328753.093046, "value": "2"}, "Brave.Core.WindowCount.2": {"sent": true, "timestamp": 1748329441.544955, "value": "2"}, "Brave.DNS.AutoSecureRequests.2": {"sent": true, "timestamp": 1748330198.045989, "value": "0"}, "Brave.DNS.SecureSetting": {"sent": true, "timestamp": 1748329463.23572, "value": "1"}, "Brave.Extensions.AdBlock": {"sent": true, "timestamp": 1748328445.152608, "value": "0"}, "Brave.Extensions.SelectManifestV2": {"sent": true, "timestamp": 1748330360.109358, "value": "0"}, "Brave.Importer.ImporterSource.2": {"sent": true, "timestamp": 1748329586.124456, "value": "0"}, "Brave.NTP.CustomizeUsageStatus.2": {"sent": true, "timestamp": 1748330010.332204, "value": "2"}, "Brave.NTP.DefaultPage": {"sent": true, "timestamp": 1748329685.995941, "value": "0"}, "Brave.NTP.NewTabsCreated.3": {"sent": true, "timestamp": 1748328710.091825, "value": "5"}, "Brave.NTP.SponsoredMediaType": {"sent": true, "timestamp": 1748329185.956435, "value": "1"}, "Brave.Omnibox.SearchCount.NonRewards": {"sent": true, "timestamp": 1748329453.775388, "value": "0"}, "Brave.P3A.SentAnswersCount": {"sent": true, "timestamp": 1748330444.759204, "value": "3"}, "Brave.Search.SwitchEngine": {"sent": true, "timestamp": 1748328483.809964, "value": "0"}, "Brave.Search.WebDiscoveryAndAds": {"sent": true, "timestamp": 1748329019.305713, "value": "0"}, "Brave.Search.WidgetDefault": {"sent": true, "timestamp": 1748328446.362428, "value": "0"}, "Brave.Shields.AdBlockSetting": {"sent": true, "timestamp": 1748330285.509813, "value": "1"}, "Brave.Shields.DomainAdsSettingsAboveGlobal": {"sent": true, "timestamp": 1748330117.198813, "value": "0"}, "Brave.Shields.DomainAdsSettingsBelowGlobal": {"sent": true, "timestamp": 1748329867.69086, "value": "0"}, "Brave.Shields.FingerprintBlockSetting": {"sent": true, "timestamp": 1748329462.002235, "value": "1"}, "Brave.Shields.UsageStatus": {"sent": true, "timestamp": 1748328452.589758, "value": "0"}, "Brave.Sync.Status.2": {"sent": true, "timestamp": 1748330327.736329, "value": "0"}, "Brave.Today.NewUserReturning": {"sent": true, "timestamp": 1748328633.238379, "value": "0"}, "Brave.Today.NonRewardsAdViews": {"sent": true, "timestamp": 1748329319.647603, "value": "0"}, "Brave.Today.WeeklySessionCount": {"sent": true, "timestamp": 1748330128.405962, "value": "0"}, "Brave.Today.WeeklyTotalCardViews": {"sent": true, "timestamp": 1748329370.867031, "value": "0"}, "Brave.Wallet.NewUserReturning": {"sent": true, "timestamp": 1748330020.547395, "value": "0"}}, "logs_constellation_prep_express": {"Brave.Core.UsageDaily": {"sent": true, "timestamp": 1748680953.840913, "value": "1"}, "Brave.Search.DefaultEngine.4": {"sent": true, "timestamp": 1748680913.19214, "value": "1"}, "Brave.Search.WebDiscoveryEnabled": {"sent": false, "value": "0"}, "Brave.Today.EnabledSetting": {"sent": true, "timestamp": 1748680972.075592, "value": "0"}, "campaignId.50eaae84-2c4e-4942-b009-6634ae8ffad0.aware": {"sent": true, "timestamp": 1748682026.886528, "value": "1"}, "creativeInstanceId.total.count": {"sent": true, "timestamp": 1748680872.772298, "value": "2"}}, "logs_constellation_prep_slow": {"Brave.Core.PrimaryLang": {"sent": true, "timestamp": 1748095301.944592, "value": "37"}, "Brave.Core.ProfileCount": {"sent": true, "timestamp": 1748095415.504157, "value": "5"}, "Brave.Core.UsageMonthly": {"sent": true, "timestamp": 1748095518.227984, "value": "1"}, "Brave.Extensions.ManifestV2": {"sent": true, "timestamp": 1748095321.209034, "value": "0"}, "Brave.Search.SearchSuggest": {"sent": true, "timestamp": 1748095331.275978, "value": "1"}, "Brave.Shields.ForgetFirstParty": {"sent": true, "timestamp": 1748095433.017219, "value": "0"}, "Brave.Speedreader.EnabledSites": {"sent": true, "timestamp": 1748095423.802531, "value": "0"}, "Brave.SplitView.UsageMonthly": {"sent": true, "timestamp": 1748095201.719881, "value": "0"}, "Brave.Sync.EnabledTypes": {"sent": true, "timestamp": 1748098907.644806, "value": "3"}, "Brave.Sync.SyncedObjectsCount.2": {"sent": true, "timestamp": 1748096355.075423, "value": "0"}}, "logs_express": {"Brave.Core.UsageDaily": {"sent": false, "value": "1"}, "Brave.Search.DefaultEngine.4": {"sent": false, "value": "8"}, "Brave.Today.EnabledSetting": {"sent": false, "value": "0"}, "campaignId.60261cfb-c5f5-4353-a29d-ccca18d646e3.aware": {"sent": false, "value": "1"}, "creativeInstanceId.total.count": {"sent": false, "value": "0"}}, "logs_slow": {"Brave.Core.ProfileCount": {"sent": false, "value": "1"}, "Brave.Core.UsageMonthly": {"sent": false, "value": "1"}, "Brave.Extensions.ManifestV2": {"sent": false, "value": "0"}, "Brave.Search.SearchSuggest": {"sent": false, "value": "1"}, "Brave.Shields.ForgetFirstParty": {"sent": false, "value": "0"}, "Brave.Speedreader.EnabledSites": {"sent": false, "value": "0"}, "Brave.SplitView.UsageMonthly": {"sent": false, "value": "0"}}}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "profile": {"info_cache": {"Default": {"active_time": **********.097366, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_26", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 12, "name": "<PERSON><PERSON><PERSON>", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 1": {"active_time": **********.754686, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_56", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 1, "name": "amazon", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 10": {"active_time": **********.43934, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_65", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 13, "name": "youtube", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 11": {"active_time": **********.351017, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_66", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 4, "name": "dynamous", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 12": {"active_time": **********.776114, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_67", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 10, "name": "n8n", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 13": {"active_time": **********.004264, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_68", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 14, "name": "claude", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 14": {"active_time": **********.654563, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_69", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 15, "name": "apps_script", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 2": {"active_time": **********.12417, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_57", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 11, "name": "scraping", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 3": {"active_time": **********.47023, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_58", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 3, "name": "chatgpt", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 4": {"active_time": **********.895748, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_59", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 9, "name": "mail-wa", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 5": {"active_time": **********.749414, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_60", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 2, "name": "ampparit", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 6": {"active_time": **********.718356, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_61", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 6, "name": "facebook", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 7": {"active_time": **********.853384, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_62", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 5, "name": "e<PERSON><PERSON>d", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 8": {"active_time": **********.655799, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_63", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 7, "name": "github", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}, "Profile 9": {"active_time": **********.290392, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_64", "background_apps": false, "default_avatar_fill_color": -1842200, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_using_default_avatar": false, "is_using_default_name": false, "managed_user_id": "", "metrics_bucket_index": 8, "name": "just brave", "profile_color_seed": -********, "profile_highlight_color": -1842200, "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "last_used": "Profile 14", "metrics": {"next_bucket_index": 16}, "picker_shown": true, "profile_counts_reported": "*****************", "profiles_order": ["Profile 1", "Profile 5", "Profile 3", "Profile 11", "Profile 7", "Profile 6", "Profile 8", "Profile 9", "Profile 4", "Profile 12", "Profile 2", "<PERSON><PERSON><PERSON>", "Profile 10", "Profile 13", "Profile 14"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "session_id_generator_last_value": "*********", "signin": {"active_accounts_last_emitted": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_frozen": 0, "discards_proactive": 0, "discards_suggested": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 15, "reloads_external": 0, "reloads_frozen": 0, "reloads_proactive": 0, "reloads_suggested": 0, "reloads_urgent": 0, "total_tab_count_max": 58, "window_count_max": 11}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"adcocjohghhfpidemphmcmlmhnfgikei": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.374", "pf": "27b1cf98-933f-4a5a-842f-2c960c1577c8", "pv": "1.0.378"}, "afalakplffnnnlkncjhbmahjfjhmlkal": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.1179", "pf": "2979884f-9d30-4b40-bb2c-f12751d4a4eb", "pv": "1.0.1180"}, "aoojcmojmmcbpfgoecoadbdpnagfchel": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "0f408715-1d49-415e-97be-a47b7d82bf50", "pv": "1.0.19"}, "bfpgedeaaibpoidldhjcknekahbikncb": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.13253", "pf": "d4ed113e-02aa-4f51-9a0b-0a74ed0e2509", "pv": "1.0.13254"}, "cdbbhgbmjhfnhnmgeddbliobbofkgdhe": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.14712", "pf": "db9f1de3-db25-4021-b2ed-16e4c7c39e3a", "pv": "1.0.14713"}, "efniojlnjndmcbiieegkicadnoecjjef": {"cohort": "1:18ql:", "cohortname": "Auto Stage3", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1321", "pf": "0baa93c0-463e-413c-a53f-022b1252294a", "pv": "1326"}, "gccbbckogglekeggclmmekihdgdpdgoe": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.2035", "pf": "a474c5c8-938f-4eb8-96ad-71c6e5eb4d1e", "pv": "1.0.2038"}, "ggkkehgbnfjpeggfpleeakpidbkibbmn": {"cohort": "1:ut9/1a0f:", "cohortname": "M108 and Above", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "2025.5.21.1142", "pf": "20f3e7fc-3b85-4c1e-940a-f2e242832e57", "pv": "2025.5.26.1183"}, "giekcmmlnklenlaomppkphknjmnnpneh": {"cohort": "1:j5l:", "cohortname": "Auto", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "853d2e7f-ece5-4362-9d07-7d66dbc5f087", "pv": "7"}, "gkboaolpopklhgplhaaiboijnklogmbc": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "e8e9f38d-c935-4138-a931-1e11ed18f9aa", "pv": "1.0.77"}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "1:2tdl:", "cohortname": "Stable", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "9c82115d-2017-4565-9d8c-8e8abc3312f6", "pv": "3"}, "hfnkpimlhhgieaddgfemjhofmfblmnib": {"cohort": "1:287f:", "cohortname": "Auto full", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "9824", "pf": "67d0cea1-a109-4a12-8cb7-d614623bebad", "pv": "9828"}, "iblokdlgekdjophgeonmanpnjihcjkjj": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "2b8e8a12-5471-466c-938f-3627fe885682", "pv": "1.0.109"}, "iodkpdagapdfkphljnddpjlldadblomo": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.12433", "pf": "8dea27ce-7c03-49b7-a565-3eb7016d7c33", "pv": "1.0.12434"}, "jamhcnnkihinmdlkakkaopbjbbcngflc": {"cohort": "1:wvr:", "cohortname": "Auto", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "df02a52c-2d89-420f-a244-6c0d74e8ea56", "pv": "120.0.6050.0"}, "jflhchccmppkfebkiaminageehmchikm": {"cohort": "1:26yf:", "cohortname": "Stable", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "2025.5.28.1", "pf": "6197b606-165f-4dc2-928d-54ef0b67b4a4", "pv": "2025.5.29.1"}, "jflookgnkcckhobaglndicnbbgbonegd": {"cohort": "1:s7x:", "cohortname": "Auto", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "97861e90-b02e-4584-a2f8-db0c15be1f7b", "pv": "3070"}, "khaoiebndkojlmppeemjhbpbandiljpe": {"cohort": "1:cux:", "cohortname": "Auto", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "29af6768-cf91-49d5-bdee-6ee109df24d6", "pv": "67"}, "laoigpblnllgcgjnjnllmfolckpjlhki": {"cohort": "1:10zr:", "cohortname": "Auto", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "1.0.7.1652906823", "pf": "bb1173d3-7488-44d9-ab61-ecbdea815834", "pv": "1.0.7.1744928549"}, "mfddibmblmbccpadfndgakiopmmhebop": {"dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "3156b691-36c0-4ad5-997c-2e9a025e6b58", "pv": "1.0.107"}, "obedbbhbpmojnkanicioggnmelmoomoc": {"cohort": "1:s6f:", "cohortname": "Auto", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "20250504.758440665.14", "pf": "3292ec48-61b6-464a-8815-da92268bc18a", "pv": "20250522.762470205.14"}, "ojhpjlocmbogdgmfpkhlaaeamibhnphh": {"cohort": "1:w0x:", "cohortname": "All users", "dlrc": 6725, "fp": "", "installdate": 6718, "max_pv": "0.0.0.0", "pf": "4cb7ffcc-67dc-4c0a-8877-87ceec9e07b1", "pv": "3"}}}, "updateclientlastupdatecheckerror": -106, "updateclientlastupdatecheckerrorcategory": 5, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"limited_entropy_randomization_source": "A378561C169FDACCABD5017B75BC0497", "low_entropy_source3": 1659, "pseudo_low_entropy_source": 6418, "session_id": 66, "stability": {"browser_last_live_timestamp": "13393156331937103", "exited_cleanly": true, "stats_buildtime": "1748489916", "stats_version": "137.1.79.118-64-devel"}}, "variations_compressed_seed": "safe_seed_content", "variations_country": "fi", "variations_crash_streak": 0, "variations_failed_to_fetch_seed_streak": 0, "variations_last_fetch_time": "13393156323752606", "variations_limited_entropy_synthetic_trial_seed_v2": "52", "variations_permanent_consistency_country": ["137.1.79.118", "fi"], "variations_safe_compressed_seed": "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*********************************************************+R1G+8A/UxDAUxLBgLhfDZ5bxwafme69xHtun4l3QUWqL6M7wyVNBuTnU96HkK/XfDHY3FIiKumhmQ3HApPu9fxWDc/1Bi61vBQH0NwhvXTg8bpENBqv5sojtdYfoUcbHqxQACuY1W2CSYF9AMMJkMSmipRH6pHyzQMIBd2DYqIa9cYvQm9RwqBB3oLt/WBj0x9HmBsqeuw9r13L42gHntgvMj4zumAyskvYMKv/NwmdDLudPL7/XEInWfinvJtbyi2qBVWd9P/786qS++7waZn/hM/Kib8W1aKqvZ9TisGv+fjXQvBr6X0fzv9Lu4BO849GWgPiWO5AzqgdbQqd4qmF13Fti38fVTTOoTPielIKYpVgpD5I54RdH6BbmxWeBIL/z7JNuiQo3I5kGhPi7qx8Y7OyYuN2i/Wp4bb8yfkWxGT0i0LInSw0/zacrxzMWdTCB/qFSAzREP5aQve3wFginunuAt1b9b8gSszzh2NvFan+UvQrd/m3KYwBdbwWT3bki9Gf8zhAVNqfxu2PMR4pgzuFIEcpaOH0zM8kdOcA4cJhvG9EsrJS0bKHVlscnX0jwpX8mn5YoDi71cGMj0mltV21O+gqw3wgudXFb1zair4P83zXKb/DJxdGizJnhh+WqmpBOD1/I6HZLZfuYL/JtAqDLf+UrtjY60yqok+CT5RKjw1LpRPBxxIx37WXc0ohTsR+/m2R70JSviB320CzZK1Z61CXfz0c9vi8ScnohgejoKNNSr8PfXu+i4nzvWquVKoY5bxRBLVasCrbeDtnFXHRrJaqar5UBFCL1ZK6/X+oOtYOs0EAAA==", "variations_safe_seed_date": "13393155565000000", "variations_safe_seed_fetch_time": "13393155566330963", "variations_safe_seed_locale": "en-US", "variations_safe_seed_milestone": 136, "variations_safe_seed_permanent_consistency_country": "fi", "variations_safe_seed_session_consistency_country": "fi", "variations_safe_seed_signature": "MEUCIQD3DtJHMmGAj+D9Pohvjcv/El2RoX3LxUbLLIwkMD6K7QIgQ1l0nBAre7r8qcdzur7A4SeYIsnBogB268L7WH6Fnf4=", "variations_seed_date": "13393156323000000", "variations_seed_milestone": 136, "variations_seed_signature": "MEUCIQD3DtJHMmGAj+D9Pohvjcv/El2RoX3LxUbLLIwkMD6K7QIgQ1l0nBAre7r8qcdzur7A4SeYIsnBogB268L7WH6Fnf4=", "was": {"restarted": false}}