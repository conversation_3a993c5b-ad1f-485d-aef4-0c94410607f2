#!/bin/bash

echo "=== Detecting running Brave processes ==="
brave_procs=$(pgrep -a brave-browser)

if [[ -z "$brave_procs" ]]; then
    echo "No Brave processes found."
    exit 0
fi

echo
echo "$brave_procs"
echo

read -p "Force-kill ALL Brave processes? This will close ALL windows WITHOUT confirmation. (y/n): " confirm

if [[ "$confirm" =~ ^[Yy]$ ]]; then
    echo "Force-killing all Brave processes (SIGKILL)..."
    pkill -9 -f brave-browser
    echo "Done."
else
    echo "Cancelled."
    exit 0
fi

