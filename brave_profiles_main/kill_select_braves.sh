#!/bin/bash

echo "=== Checking for running Brave browser processes ==="
ps -eo pid,cmd | grep -i '[b]rave-browser' || {
    echo "No Brave processes running."
    exit 0
}

echo
echo "=== Detected Brave processes ==="
ps -eo pid,cmd | grep -i '[b]rave-browser'

echo
read -p "Kill all Brave processes? (y/n): " confirm_all

if [[ "$confirm_all" =~ ^[Yy]$ ]]; then
    echo "Killing all Brave processes..."
    pkill -f brave-browser
    echo "Done."
    exit 0
fi

echo
echo "=== Selective kill mode ==="

# Extract profile directories (if visible in the process list)
ps -eo pid,cmd | grep -i '[b]rave-browser' | grep -- '--profile-directory=' | awk '
{
    match($0, /--profile-directory=([^ ]+)/, arr)
    if (arr[1] != "") {
        print $1 " " arr[1]
    }
}' > /tmp/brave_profiles.txt

if [[ ! -s /tmp/brave_profiles.txt ]]; then
    echo "No individual profiles found. Exiting."
    exit 1
fi

cat /tmp/brave_profiles.txt

echo
read -p "Enter the name of the profile to kill (e.g., Profile 1): " target_profile

# Kill processes matching that profile
grep "$target_profile" /tmp/brave_profiles.txt | awk '{print $1}' | while read pid; do
    echo "Killing PID $pid ($target_profile)..."
    kill "$pid"
done

rm /tmp/brave_profiles.txt
echo "Done."

