"""
Brave Profiles Management Package

This package provides utilities for managing Brave browser profiles,
including configuration management, batch file generation, and profile synchronization.
"""

__version__ = "0.1.0"
__author__ = "Brave Profiles Team"

# Import main classes and functions for easy access
from .config import BravePaths
from .profile_types import Profile, ProfileOptions
from .json_utils import (
    JsonValue,
    read_json_file,
    read_json_file_safe,
    write_json_file,
    validate_json_dict,
    validate_json_list,
)
from .file_utils import copy_file, make_folder_with_gitkeep

__all__ = [
    "BravePaths",
    "Profile",
    "ProfileOptions",
    "JsonValue",
    "read_json_file",
    "read_json_file_safe",
    "write_json_file",
    "validate_json_dict",
    "validate_json_list",
    "copy_file",
    "make_folder_with_gitkeep",
]
