# scripts/generate_config.py
from dataclasses import dataclass
from pathlib import Path
from dotenv import dotenv_values
from brave_profiles_main.file_utils import BravePaths, init_dirs
BASE_DIR = Path(__file__).resolve().parent


def generate_config_py(paths: dict[str, Path], file_name: str = "config.py"):
    lines = [
        "from pathlib import Path",
        "from dataclasses import dataclass\n",
        "@dataclass",
        "class BravePaths:",
        "    root: Path = Path.home()",
        "    base_dir: Path = Path(__file__).resolve().parent\n",
    ]

    for key, value in paths.items():
        prop_name = key.lower()
        path_str = f"Path(r'{value}')"
        lines.append(f"    @property")
        lines.append(f"    def {prop_name}(self) -> Path:")
        lines.append(f"        return {path_str}\n")

    lines.append("    @staticmethod")
    lines.append("    def this_file() -> Path:")
    lines.append("        return Path(__file__)\n")

    # Utility to get all paths from a BravePaths instance
    lines.append("def get_all_paths(instance: BravePaths) -> dict[str, Path]:")
    lines.append("    import inspect")
    lines.append("    from dataclasses import fields")
    lines.append("    cls = type(instance)")
    lines.append("    result = {}\n")
    lines.append("    # Get regular dataclass fields")
    lines.append("    for field in fields(cls):")
    lines.append("        result[field.name] = getattr(instance, field.name)\n")
    lines.append("    # Get @property methods")
    lines.append("    for name, member in inspect.getmembers(cls):")
    lines.append("        if isinstance(member, property):")
    lines.append("            try:")
    lines.append("                result[name] = getattr(instance, name)")
    lines.append("            except Exception as e:")
    lines.append("                result[name] = f'<Error accessing property: {e}>'\n")
    lines.append("    return result\n")

    config_file_path = BASE_DIR / file_name
    config_file_path.write_text("\n".join(lines))
    print(f"✅ Config file written to {config_file_path}")


if __name__ == "__main__":
    folder_name_vs_path = init_dirs()
    print(folder_name_vs_path)
    generate_config_py(folder_name_vs_path)
