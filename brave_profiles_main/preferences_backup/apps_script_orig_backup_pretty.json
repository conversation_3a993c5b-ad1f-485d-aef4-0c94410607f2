{"accessibility": {"captions": {"headless_caption_enabled": false}}, "alternate_error_pages": {"backup": false}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "13394537677433471"}, "brave": {"accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "AltGr", "Alt"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "brave_ads": {"notification_ads": [], "should_allow_ads_subdivision_targeting": false, "state": {"has_migrated": {"client": {"v7": true}, "confirmations": {"v8": true}, "v2": true}}}, "brave_search": {"last-used-ntp-search-engine": "search.brave.com"}, "default_accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "Alt", "Alt", "AltGr"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "default_private_search_provider_data": {"alternate_urls": [], "contextual_search_url": "", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://cdn.search.brave.com/serp/favicon.ico", "featured_by_policy": false, "id": "0", "image_search_branding_label": "", "image_translate_source_language_param_key": "", "image_translate_target_language_param_key": "", "image_translate_url": "", "image_url": "", "image_url_post_params": "", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": ":br", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": false, "prefetch_likely_navigations": false, "prepopulate_id": 550, "safe_for_autoreplace": true, "search_intent_params": [], "search_url_post_params": "", "short_name": "Brave", "starter_pack_id": 0, "suggestions_url": "https://search.brave.com/api/suggest?q={searchTerms}&rich=true&source=desktop", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "https://search.brave.com/search?q={searchTerms}&source=desktop", "usage_count": 0}, "default_private_search_provider_guid": "485bf7d3-0215-45af-87dc-************", "enable_media_router_on_restart": true, "enable_window_closing_confirm": true, "migrated_search_default_in_jp": true, "rewards": {"notifications": "{\"displayed\":[],\"notifications\":[]}", "scheduled_captcha": {"failed_attempts": 0, "id": "", "paused": false, "payment_id": ""}}, "search": {"default_version": 32}, "shields_settings_version": 4, "stats": {"ads_blocked": "979", "bandwidth_saved_bytes": "27961458", "daily_saving_predictions_bytes": [{"day": **********.0, "value": 769605.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "today": {"p3a_total_card_views": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_card_visits": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_sidebar_filter_usages": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "wallet": {"aurora_mainnet_migrated": true, "custom_networks": {"goerli_migrated": true}, "eip1559_chains_migrated": true, "is_compressed_nft_migrated": true, "is_spl_token_program_migrated": true, "keyrings": {}, "last_transaction_sent_time_dict": {}}, "webtorrent_enabled": false, "weekly_storage": {"Brave": {"P2A": {"new_tab_page_ad": {"opportunities": [{"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 3.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}}}, "search_count": [{"day": **********.0, "value": 2.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}}, "brave_shields": {"p3a_ads_allow_domain_count": 0, "p3a_ads_standard_domain_count": 0, "p3a_ads_strict_domain_count": 0, "p3a_first_reported_revision": 3, "p3a_fp_allow_domain_count": 0, "p3a_fp_standard_domain_count": 0, "p3a_fp_strict_domain_count": 0}, "brave_sync_v2": {"reset_devices_progress_token_time": "13393156323685754"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1080, "left": 1920, "maximized": true, "right": 3840, "top": 0, "work_area_bottom": 1080, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}, "window_placement_popup": {"bottom": 904, "left": 2561, "maximized": false, "right": 3211, "top": 147, "work_area_bottom": 1080, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "13394537677433509", "countryid_at_install": 17993, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "download_bubble": {"partial_view_impressions": 2}, "enterprise_profile_guid": "9a0291a1-ac6b-40c4-b72c-e6e140f7051d", "ephemeral_storage": {"first_party_storage_origins_to_cleanup": []}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "************", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Löydät hienoja <PERSON>, p<PERSON><PERSON><PERSON>, laaj<PERSON>nuks<PERSON> ja tee<PERSON><PERSON>.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Verkkokauppa", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/opt/brave.com/brave/resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://brave-resources/*", "chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://brave-resources chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://brave-resources/", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/opt/brave.com/brave/resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnojpmjdmbbfmejpflffifhffcmidifd": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["*://*/*", "<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"all_frames": true, "js": ["out/webstore.bundle.js"], "matches": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"], "run_at": "document_start"}], "content_security_policy": "default-src 'self'; font-src 'self' data:; script-src 'self' 'wasm-eval'; style-src 'unsafe-inline'; img-src 'self' data: chrome://favicon/; connect-src * data: blob: filesystem:;", "default_locale": "en_US", "description": "__MSG_appName__", "icons": {"128": "assets/img/icon-128.png", "16": "assets/img/icon-16.png", "256": "assets/img/icon-256.png", "32": "assets/img/icon-32.png", "48": "assets/img/icon-48.png", "64": "assets/img/icon-64.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAupOLMy5Fd4dCSOtjcApsAQOnuBdTs+OvBVt/3P93noIrf068x0xXkvxbn+fpigcqfNamiJ5CjGyfx9zAIs7zcHwbxjOw0Uih4SllfgtK+svNTeE0r5atMWE0xR489BvsqNuPSxYJUmW28JqhaSZ4SabYrRx114KcU6ko7hkjyPkjQa3P+chStJjIKYgu5tWBiMJp5QVLelKoM+xkY6S7efvJ8AfajxCViLGyDQPDviGr2D0VvIBob0D1ZmAoTvYOWafcNCaqaejPDybFtuLFX3pZBqfyOCyyzGhucyCmfBXJALKbhjRAqN5glNsUmGhhPK87TuGATQfVuZtenMvXMQIDAQAB", "manifest_version": 2, "name": "Brave", "permissions": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "webNavigation", "webRequest", "*://*/*", "chrome://favicon/*", "webDiscovery", "webRequestBlocking", "unlimitedStorage", "<all_urls>"], "version": "1.0.0"}, "path": "/opt/brave.com/brave/resources/brave_extension", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.408988, "hash": "qU1HudHVcbAuCazE7xrsPT8goaY=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"<PERSON><PERSON>\",\"lasser<PERSON>@gmail.com\",\"https://lh3.googleusercontent.com/-me02THWXDI8/AAAAAAAAAAI/AAAAAAAAAAA/5kc2TtKDRKM/s48-c/photo.jpg\",1,1,0,null,1,\"108135753157727873256\",null,null,null,null,1],[\"gaia.l.a\",1,\"<PERSON><PERSON>\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-JSetWKByyrE/AAAAAAAAAAI/AAAAAAAAAAA/aFYyG83xltc/s48-c/photo.jpg\",0,null,null,null,0,\"109845053072526052504\",null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "com.brave.linux"}, "google": {"services": {"signin_scoped_device_id": "f504037f-2e22-4f82-ac5e-863a483ff922"}}, "https_upgrade_navigations": {"2025-05-31": 10, "2025-06-01": 76, "2025-06-02": 104, "2025-06-04": 40, "2025-06-15": 10}, "in_product_help": {"new_badge": {"ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13393156323510720", "recent_session_start_times": ["13394490335579217", "13394451277342232", "13393495027904497", "13393328654196923", "13393279887958766", "13393256373578539", "13393156323510720"], "session_last_active_time": "13394497128954523", "session_start_time": "13394490335579217"}, "intl": {"accept_languages": "fi,en-US,en", "selected_languages": "fi,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "language_model_counters": {"en": 185, "fi": 22}, "media": {"device_id_salt": "050285F6CBC4F43A0EC6A9F75565C0BF", "engagement": {"schema_version": 5}}, "media_router": {"enable_media_router": true, "receiver_id_hash_token": "A0pj40/k47lEczoDcwpVjmPQ7J+ZWbrXFJFczdBNGb20xEmQmsdUC1YiX+RvHWgla6WPiGZGylFgsXU3yQfy5g=="}, "ntp": {"num_personal_suggestions": 6}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "pinned_tabs": [], "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false, "m1": {"ad_measurement_enabled": false, "fledge_enabled": false, "topics_enabled": false}}, "profile": {"avatar_index": "69", "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {"https://[*.]google.com,https://[*.]google.com": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": 1}}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://developers.google.com:443,*": {"last_modified": "13393504631153029", "setting": {"https://developers.google.com/": {"couldShowBannerEvents": 1.3393156326135704e+16, "next_install_text_animation": {"delay": "691200000000", "last_shown": "13393504631153016"}}}}, "https://drive.google.com:443,*": {"last_modified": "13393504625235565", "setting": {"https://drive.google.com/": {"next_install_text_animation": {"delay": "345600000000", "last_shown": "13393504625235544"}}, "https://drive.google.com/?lfhs=2": {"couldShowBannerEvents": 1.339315645839672e+16}}}, "https://github.com:443,*": {"last_modified": "13393292942317167", "setting": {"https://github.com/": {"couldShowBannerEvents": 1.339329294116343e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13393292942317155"}}}}, "https://www.typescriptlang.org:443,*": {"last_modified": "13393495274508160", "setting": {"https://www.typescriptlang.org/": {"couldShowBannerEvents": 1.339329295759171e+16, "next_install_text_animation": {"delay": "172800000000", "last_shown": "13393495274508152"}}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "braveShields": {}, "braveShieldsMetadata": {"http://0.0.0.0,*": {"last_modified": "13394452538764149", "setting": {"farbling_token": "EA03F1BB3244C511EA45930557B6BFD5"}}, "https://[*.]astral.sh,*": {"last_modified": "13394456490850058", "setting": {"farbling_token": "7C5F651AA6BF8B35848603193865A025"}}, "https://[*.]brave.com,*": {"last_modified": "13394453202646040", "setting": {"farbling_token": "5E43B9BBCE4E965A2E5D8918BA79E7C6"}}, "https://[*.]google.com,*": {"last_modified": "13393502236804887", "setting": {"farbling_token": "A2E2571F51F22D91E581BDE46488DE01"}}, "https://[*.]typescriptlang.org,*": {"last_modified": "13394451284508362", "setting": {"farbling_token": "FE8FD41146674D21B9D5343A1889824D"}}}, "braveSpeedreader": {}, "brave_ethereum": {}, "brave_google_sign_in": {}, "brave_localhost_access": {}, "brave_open_ai_chat": {}, "brave_remember_1p_storage": {}, "brave_solana": {}, "brave_webcompat_audio": {}, "brave_webcompat_canvas": {}, "brave_webcompat_device_memory": {}, "brave_webcompat_event_source_pool": {}, "brave_webcompat_font": {}, "brave_webcompat_hardware_concurrency": {}, "brave_webcompat_keyboard": {}, "brave_webcompat_language": {}, "brave_webcompat_media_devices": {}, "brave_webcompat_none": {}, "brave_webcompat_plugins": {}, "brave_webcompat_screen": {}, "brave_webcompat_speech_synthesis": {}, "brave_webcompat_usb_device_serial_number": {}, "brave_webcompat_user_agent": {}, "brave_webcompat_web_sockets_pool": {}, "brave_webcompat_webgl": {}, "brave_webcompat_webgl2": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://calendar.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://docs.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://drive.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://gds.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://myaccount.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://script.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "cosmeticFiltering": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "fingerprintingV2": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "httpUpgradableResources": {}, "http_allowed": {}, "httpsUpgrades": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://0.0.0.0:8000,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://accounts.google.com:443,*": {"expiration": "********979521495", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 10}}, "https://brave.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://developers.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://docs.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://gds.google.com:443,*": {"expiration": "********168350430", "last_modified": "13393495168350433", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://github.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://mapon.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://myaccount.google.com:443,*": {"expiration": "********354559049", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://script.google.com:443,*": {"expiration": "********979578609", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 13}}, "https://support.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 1.339334003677632e+16, "mediaPlaybacks": 1, "visits": 7}}, "https://www.mapon.com:443,*": {"expiration": "13401272428386017", "last_modified": "13393496428386018", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.typescriptlang.org:443,*": {"expiration": "13401068985978660", "last_modified": "13393292985978662", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.veronmaksajat.fi:443,*": {"expiration": "13401280559810566", "last_modified": "13393504559810569", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://zapier.com:443,*": {"expiration": "13401117260521883", "last_modified": "13393341260521885", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "referrers": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "shieldsAds": {}, "shieldsCookiesV3": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394505603504632e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 18.**************}}, "http://0.0.0.0:8000,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394504647764732e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 8.1}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394462927086188e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 9.**************}}, "https://api-dashboard.search.brave.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394502496707156e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.1}}, "https://calendar.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394471599931736e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 19.***************}}, "https://developers.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394364208510988e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 21.***************}}, "https://docs.astral.sh:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.**********523388e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.3000000000000003}}, "https://docs.google.com:443,*": {"last_modified": "13394534504523851", "setting": {"lastEngagementTime": 1.3394464391951344e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 33.663459101195976}}, "https://drive.google.com:443,*": {"last_modified": "13394534504523862", "setting": {"lastEngagementTime": 1.3394432792899724e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 17.***************}}, "https://gds.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.339446211696942e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://mapon.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394463572236352e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.1}}, "https://myaccount.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394462283206064e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://script.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.339450056549044e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 65.**************}}, "https://support.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394401168535112e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394505610555732e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.**************}}, "https://www.mapon.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394463379016592e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "https://www.typescriptlang.org:443,*": {"last_modified": "13394534504524006", "setting": {"lastEngagementTime": 1.3394352668493552e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 6.299999999999999}}, "https://www.veronmaksajat.fi:443,*": {"last_modified": "13394534504523983", "setting": {"lastEngagementTime": 1.3394463337704728e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://zapier.com:443,*": {"last_modified": "13394534504523969", "setting": {"lastEngagementTime": 1.339439973553532e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.1}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.1.79.118", "creation_time": "13393156323472151", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "1**********523387", "last_time_obsolete_http_credentials_removed": 1748682820.072669, "last_time_password_store_metrics_reported": 1749977707.322289, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "apps_script", "password_hash_data_list": [{"hash": "djExTi+LKmjutD9ELZkNp0oBKQ==", "is_gaia": "djExp4kSF4WDwCzHnpazBtYnmQ==", "last_signin": 1748682816.745165, "salt_length": "djEx/2pM+cppgsNJSG7nmvO8vYZ1ylj2iFq8bpKLvZJQuYY=", "username": "djExQ5rdL6J9qCmpyjFbrSc4WGeQVrT5avIQV1lwM357hJQcoQXxyBm28Sl7sQjFpTKa"}, {"hash": "djExMgULmPCWZgJ2Aw0w8WKoRQ==", "is_gaia": "djExp4kSF4WDwCzHnpazBtYnmQ==", "last_signin": 1749021721.911982, "salt_length": "djExBA/olmkzwfUY0tr4xBaFYnKWA70DTAkOhugf39ibcN4=", "username": "djExVkifBNcpGHvB++L2qIPhYhZ59XXY9IhpWgaA66sGEZ4="}], "safety_hub_menu_notifications": {"extensions": {"isCurrentlyActive": false, "result": {"timestamp": "13394453182128004", "triggeringExtensions": []}}, "notification-permissions": {"isCurrentlyActive": false, "result": {"notificationPermissions": [], "timestamp": "13394453179526106"}}, "passwords": {"isCurrentlyActive": false, "result": {"passwordCheckOrigins": [], "timestamp": "13394453179528687"}}, "safe-browsing": {"isCurrentlyActive": false, "onlyShowAfterTime": "13394539579498459", "result": {"safeBrowsingStatus": 1, "timestamp": "13394453182127989"}}, "unused-site-permissions": {"isCurrentlyActive": false, "result": {"permissions": [], "timestamp": "13394451277455143"}}}, "were_old_google_logins_removed": true}, "protection": {"macs": {"brave": {"ad_block": {"developer_mode": "F59142B67502DC56C4D3763941A5F271E22391B134DC3833BE6C25DA10E7804C"}}, "browser": {"show_home_button": "904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"}, "default_search_provider_data": {"template_url_data": "575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"}, "enterprise_signin": {"policy_recovery_token": "7D3124ECAF7E96407EB65EAF5A43B02C7EE5F2D4A9FA38A9F371F9E1B74D6383"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "47C76D2CEFED8783C7F914B15E53D88255350680B788D6A175A2A8B6D3DA6BD9", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "FC5A59FD5BFD9DAFC191F6A4C82226E436F0A272BEE54F0C0EE7689DD6C2C8E9", "mnojpmjdmbbfmejpflffifhffcmidifd": "A98209FD65218146046A6AA330AC18FBABCA49FB07141EDE620E09756A567CDC"}, "ui": {"developer_mode": "55A29C051727FCAC1BDA847CCDE40838B2068CFA589E88819EA9FB17E4BD7320"}}, "google": {"services": {"account_id": "E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486", "last_signed_in_username": "82DB8D884695C643C31778B7B50DBB376848E2F81B5A1AECDA34FD448CECD10D", "last_username": "24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}}, "homepage": "B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E", "homepage_is_newtabpage": "3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119", "media": {"storage_id_salt": "E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"}, "pinned_tabs": "699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8", "prefs": {"preference_reset_time": "95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"}, "safebrowsing": {"incidents_sent": "569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"}, "search_provider_overrides": "1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD", "session": {"restore_on_startup": "F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E", "startup_urls": "8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}}, "safebrowsing": {"event_timestamps": {"0": {"12": ["13393497408", "13393497455"]}}, "metrics_last_log_time": "13394537677", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "savefile": {"default_directory": "/home/<USER>/Downloads"}, "selectfile": {"last_directory": "/home/<USER>/Dropbox/asennuspk/weasy_print_koe"}, "sessions": {"event_log": [{"restore_browser": true, "synchronous": false, "time": "13393256624492637", "type": 5}, {"errored_reading": false, "tab_count": 8, "time": "13393256624562270", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 9, "time": "13393256665362958", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393279887942813", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13393279887944775", "type": 5}, {"errored_reading": false, "tab_count": 9, "time": "13393279888020279", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 7, "time": "13393307493925759", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393328611907573", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13393328654192922", "type": 5}, {"errored_reading": false, "tab_count": 7, "time": "13393328654265294", "type": 1, "window_count": 1}, {"crashed": true, "time": "13393406178537265", "type": 0}, {"crashed": true, "time": "13393495027902236", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13393495029694439", "type": 5}, {"errored_reading": false, "tab_count": 11, "time": "13393495029717335", "type": 1, "window_count": 1}, {"crashed": true, "time": "13393668540003600", "type": 0}, {"crashed": true, "time": "13394451277321068", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394451280491244", "type": 5}, {"errored_reading": false, "tab_count": 4, "time": "13394451280503121", "type": 1, "window_count": 1}, {"crashed": true, "time": "13394534501351111", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 3, "fi": 5}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://www.typescriptlang.org/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults_with_url": {"https://www.google.com/search?q=ruff+E402&oq=ruff+E402&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIICAEQABgWGB4yDQgCEAAYhgMYgAQYigUyCggDEAAYgAQYogQyCggEEAAYgAQYogQyBwgFEAAY7wUyCggGEAAYgAQYogQyBggHEC4YQNIBCDM5MDVqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8": ")]}'\n[\"\",[\"ral 9003\",\"ruff dog\",\"ruff n tuff\",\"ruff rust\",\"ruff flake8\",\"ruff #2\",\"ruff e402\",\"brave api key\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"history\",\"history\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChcIwLgCEhEKD1JlY2VudCBzZWFyY2hlcwohCJBOEhwKGlJlbGF0ZWQgdG8gcmVjZW50IHNlYXJjaGVz\",\"google:suggestdetail\":[{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003druff+e402\\u0026deltok\\u003dAMc44K6s_SY2JP5S8QSBl6orE7w-su-bVQ\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000},{\"du\":\"/complete/deleteitems?client\\u003dchrome-omni\\u0026delq\\u003dbrave+api+key\\u0026deltok\\u003dAMc44K6DL-AnRrnc6WEkhOAKuafwre1h8g\\u0026gs_ri\\u003dchrome-ext-ansg\",\"zl\":40000}],\"google:suggesteventid\":\"-5010395956248369587\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[512,650,273,524,362,308,10],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[524,362,39],[524,362,39]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"PERSONALIZED_QUERY\",\"PERSONALIZED_QUERY\"],\"google:verbatimrelevance\":851}]"}}}