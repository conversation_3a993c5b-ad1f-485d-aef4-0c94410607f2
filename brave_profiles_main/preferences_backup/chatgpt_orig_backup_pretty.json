{"accessibility": {"captions": {"headless_caption_enabled": false}}, "alternate_error_pages": {"backup": false}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "13395401917215352"}, "brave": {"accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "AltGr", "Alt"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "branded_wallpaper_notification_dismissed": true, "brave_ads": {"notification_ads": [], "should_allow_ads_subdivision_targeting": false, "state": {"has_migrated": {"client": {"v7": true}, "confirmations": {"v8": true}, "v2": true}}}, "brave_search": {"last-used-ntp-search-engine": "search.brave.com"}, "default_accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "Alt", "Alt", "AltGr"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "default_private_search_provider_data": {"alternate_urls": [], "contextual_search_url": "", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://cdn.search.brave.com/serp/favicon.ico", "featured_by_policy": false, "id": "0", "image_search_branding_label": "", "image_translate_source_language_param_key": "", "image_translate_target_language_param_key": "", "image_translate_url": "", "image_url": "", "image_url_post_params": "", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": ":br", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": false, "prefetch_likely_navigations": false, "prepopulate_id": 550, "safe_for_autoreplace": true, "search_intent_params": [], "search_url_post_params": "", "short_name": "Brave", "starter_pack_id": 0, "suggestions_url": "https://search.brave.com/api/suggest?q={searchTerms}&rich=true&source=desktop", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "https://search.brave.com/search?q={searchTerms}&source=desktop", "usage_count": 0}, "default_private_search_provider_guid": "485bf7d3-0215-45af-87dc-************", "enable_media_router_on_restart": true, "enable_window_closing_confirm": true, "migrated_search_default_in_jp": true, "rewards": {"notifications": "{\"displayed\":[],\"notifications\":[]}", "scheduled_captcha": {"failed_attempts": 0, "id": "", "paused": false, "payment_id": ""}}, "search": {"default_version": 32}, "shields_settings_version": 4, "stats": {"ads_blocked": "2581", "bandwidth_saved_bytes": "53534852", "daily_saving_predictions_bytes": [{"day": **********.0, "value": 252098.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 4351271.0}, {"day": **********.0, "value": 995848.0}, {"day": **********.0, "value": 2512703.0}, {"day": **********.0, "value": 1708229.0}, {"day": **********.0, "value": 0.0}]}, "today": {"p3a_total_card_views": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_card_visits": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_sidebar_filter_usages": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "wallet": {"aurora_mainnet_migrated": true, "custom_networks": {"goerli_migrated": true}, "eip1559_chains_migrated": true, "is_compressed_nft_migrated": true, "is_spl_token_program_migrated": true, "keyrings": {}, "last_transaction_sent_time_dict": {}}, "webtorrent_enabled": false, "weekly_storage": {"Brave": {"P2A": {"new_tab_page_ad": {"opportunities": [{"day": **********.0, "value": 3.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 2.0}, {"day": **********.0, "value": 3.0}, {"day": **********.0, "value": 2.0}]}}}, "search_count": [{"day": **********.0, "value": 6.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 2.0}, {"day": **********.0, "value": 1.0}]}}, "brave_shields": {"p3a_ads_allow_domain_count": 0, "p3a_ads_standard_domain_count": 0, "p3a_ads_strict_domain_count": 0, "p3a_first_reported_revision": 3, "p3a_fp_allow_domain_count": 0, "p3a_fp_standard_domain_count": 0, "p3a_fp_strict_domain_count": 0}, "brave_sync_v2": {"account_deleted_notice_pending": false, "reset_devices_progress_token_time": "*****************", "seed": "djExnE88VoEjemn66hYkijwcvlJUyml3ToBvkIQKujBK+fp9I1DI4QiZsILkVwza1UhQoHw1I20usWNaeI1Fys994LJWKvXQEsDVU/H4NxNDH3+mostDEaRfG+BB0EF2d1YeasZhAZLzK2YxS8nME9TB9k2Yh7fp88ScFOp3o7wvlGcG1QfX5IkzHCdoP+svatgb"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1080, "left": 1920, "maximized": true, "right": 3840, "top": 0, "work_area_bottom": 1080, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17993, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "78", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "download_bubble": {"partial_view_impressions": 6}, "enterprise_profile_guid": "fa4cd874-b32f-4525-82cc-abe2f9a36577", "ephemeral_storage": {"first_party_storage_origins_to_cleanup": []}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "************", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Brave.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Brave Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/opt/brave.com/brave/resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://brave-resources/*", "chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://brave-resources chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://brave-resources/", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/opt/brave.com/brave/resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnojpmjdmbbfmejpflffifhffcmidifd": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["*://*/*", "<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"all_frames": true, "js": ["out/webstore.bundle.js"], "matches": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"], "run_at": "document_start"}], "content_security_policy": "default-src 'self'; font-src 'self' data:; script-src 'self' 'wasm-eval'; style-src 'unsafe-inline'; img-src 'self' data: chrome://favicon/; connect-src * data: blob: filesystem:;", "default_locale": "en_US", "description": "__MSG_appName__", "icons": {"128": "assets/img/icon-128.png", "16": "assets/img/icon-16.png", "256": "assets/img/icon-256.png", "32": "assets/img/icon-32.png", "48": "assets/img/icon-48.png", "64": "assets/img/icon-64.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAupOLMy5Fd4dCSOtjcApsAQOnuBdTs+OvBVt/3P93noIrf068x0xXkvxbn+fpigcqfNamiJ5CjGyfx9zAIs7zcHwbxjOw0Uih4SllfgtK+svNTeE0r5atMWE0xR489BvsqNuPSxYJUmW28JqhaSZ4SabYrRx114KcU6ko7hkjyPkjQa3P+chStJjIKYgu5tWBiMJp5QVLelKoM+xkY6S7efvJ8AfajxCViLGyDQPDviGr2D0VvIBob0D1ZmAoTvYOWafcNCaqaejPDybFtuLFX3pZBqfyOCyyzGhucyCmfBXJALKbhjRAqN5glNsUmGhhPK87TuGATQfVuZtenMvXMQIDAQAB", "manifest_version": 2, "name": "Brave", "permissions": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "webNavigation", "webRequest", "*://*/*", "chrome://favicon/*", "webDiscovery", "webRequestBlocking", "unlimitedStorage", "<all_urls>"], "version": "1.0.0"}, "path": "/opt/brave.com/brave/resources/brave_extension", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.107851, "hash": "zbdnZZujdLlzATrtreV+FVW/x78=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"<PERSON><PERSON>\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-JSetWKByyrE/AAAAAAAAAAI/AAAAAAAAAAA/aFYyG83xltc/s48-c/photo.jpg\",1,1,0,null,1,\"109845053072526052504\",null,null,null,null,1],[\"gaia.l.a\",1,\"<PERSON><PERSON>\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-me02THWXDI8/AAAAAAAAAAI/AAAAAAAAAAA/5kc2TtKDRKM/s48-c/photo.jpg\",0,0,1,null,1,\"108135753157727873256\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "com.brave.linux", "push_messaging_unsubscribed_entries_list": []}, "google": {"services": {"signin_scoped_device_id": "95041fdc-9ef2-47f3-8ee6-6d8a313fe747"}}, "history_clusters": {"last_selected_tab": 0}, "https_upgrade_navigations": {"2025-06-11": 10, "2025-06-12": 15, "2025-06-13": 25, "2025-06-15": 10, "2025-06-17": 10, "2025-06-18": 10, "2025-06-20": 20, "2025-06-21": 20, "2025-06-22": 20, "2025-06-23": 17, "2025-06-25": 3}, "in_product_help": {"new_badge": {"ComposeNudge": {"feature_enabled_time": "13392568065517557", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "13392568065517563", "show_count": 0, "used_count": 0}}, "policy_last_heavyweight_promo_time": "13393669974036678", "recent_session_enabled_time": "13392568065517328", "recent_session_start_times": ["13395399545001679", "13395362636436919", "13395308330407211", "13395137036028124", "13395108010563119", "13395056059945154", "13394966595933271", "13394878870253991", "13394700788450472", "13394622566510983", "13394490335579371", "13394457831364821", "13394261172367967", "13394234316002389", "13394183983231421", "13394151034982753", "13394110114338432", "13393842238044156", "13393761923164992", "13393719485365258", "13393668585210336", "13393499860820454", "13393328618358810", "13393278543200634", "13393154328765965", "13392982690224394"], "session_last_active_time": "13395400783816764", "session_start_time": "13395399545001679", "snoozed_feature": {"IPH_DiscardRing": {"first_show_time": "13393295469383073", "is_dismissed": true, "last_dismissed_by": 0, "last_show_time": "13393669974036683", "last_snooze_time": "0", "promo_index": 0, "show_count": 3, "shown_for_apps": [], "snooze_count": 0}}}, "intl": {"accept_languages": "fi,en-US,en", "selected_languages": "fi,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "language_model_counters": {"en": 246, "fi": 111}, "media": {"device_id_salt": "49AA71D1953809C7E6365A8850D4D1CD", "engagement": {"schema_version": 5}}, "media_router": {"enable_media_router": true, "receiver_id_hash_token": "tF1fr3gM5BMcZ/ISglVnlc+FAQCtXD6YD26ed2o645EWYBRMw1VPdUB1HrD9Lf2iTKULGN5tDTNpzMUJlYLzxQ=="}, "ntp": {"num_personal_suggestions": 12}, "omnibox": {"shown_count_history_scope_promo": 1}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "pinned_tabs": [], "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false, "m1": {"ad_measurement_enabled": false, "fledge_enabled": false, "topics_enabled": false}}, "profile": {"avatar_index": "58", "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://developers.google.com:443,*": {"last_modified": "********725997037", "setting": {"https://developers.google.com/": {"couldShowBannerEvents": 1.3393157774574292e+16, "next_install_text_animation": {"delay": "345600000000", "last_shown": "********725997029"}}}}, "https://drive.google.com:443,*": {"last_modified": "13393761927972642", "setting": {"https://drive.google.com/": {"next_install_text_animation": {"delay": "172800000000", "last_shown": "13393761927972634"}}, "https://drive.google.com/?lfhs=2": {"couldShowBannerEvents": 1.3393668811298504e+16}}}, "https://github.com:443,*": {"last_modified": "13394993142997439", "setting": {"https://github.com/": {"couldShowBannerEvents": 1.339315857769314e+16, "next_install_text_animation": {"delay": "1382400000000", "last_shown": "13394993142997432"}}}}, "https://photos.google.com:443,*": {"last_modified": "********577327774", "setting": {"https://photos.google.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "********577327649"}}, "https://photos.google.com/?lfhs=2": {"couldShowBannerEvents": 1.3394205577327772e+16}}}, "https://pulse.appsscript.info:443,*": {"last_modified": "13393297166738619", "setting": {"https://pulse.appsscript.info/": {"couldShowBannerEvents": 1.3393297166738616e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13393297166738482"}}}}, "https://scrapbox.io:443,*": {"last_modified": "13393297314569881", "setting": {"https://scrapbox.io/": {"couldShowBannerEvents": 1.3393297314569876e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13393297314569734"}}}}, "https://www.postman.com:443,*": {"last_modified": "13393765476985918", "setting": {"https://www.postman.com/": {"couldShowBannerEvents": 1.3393765476985916e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13393765476985807"}}}}, "https://www.reddit.com:443,*": {"last_modified": "13393669482228237", "setting": {"https://www.reddit.com/": {"couldShowBannerEvents": 1.3393295706644108e+16, "next_install_text_animation": {"delay": "172800000000", "last_shown": "13393669482228228"}}}}, "https://www.typescriptlang.org:443,*": {"last_modified": "13393157964603111", "setting": {"https://www.typescriptlang.org/": {"couldShowBannerEvents": 1.3393157964603108e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13393157964603005"}}}}, "https://www.youtube.com:443,*": {"last_modified": "13393669230284847", "setting": {"https://www.youtube.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "13393669230284724"}}, "https://www.youtube.com/?feature=ytca": {"couldShowBannerEvents": 1.3393669230284844e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "braveShields": {"www.testo.com,*": {"last_modified": "13394879439246418", "setting": 2}}, "braveShieldsMetadata": {"https://[*.]astral.sh,*": {"last_modified": "13395158075824551", "setting": {"farbling_token": "D800953AA27DA63523DE395B0229B034"}}, "https://[*.]augmentcode.com,*": {"last_modified": "13395156943736539", "setting": {"farbling_token": "D776595FA62EFE0C0270393364DD3A8C"}}, "https://[*.]chatgpt.com,*": {"last_modified": "13395063508365276", "setting": {"farbling_token": "46EB60E17ACA98A32399CDA97EF69A46"}}, "https://[*.]github.com,*": {"last_modified": "13395109816270226", "setting": {"farbling_token": "C8C6A5D657DC5853D17E7C1726B5628E"}}, "https://[*.]google.com,*": {"last_modified": "13395109810938771", "setting": {"farbling_token": "83BCA192C111CB4ADB4CD7BD96EB5F33"}}, "https://[*.]medium.com,*": {"last_modified": "13395160737423912", "setting": {"farbling_token": "B86E9D0943CEDF677E391FBAFFAB251C"}}, "https://[*.]midjourney.com,*": {"last_modified": "13395137632428581", "setting": {"farbling_token": "46067E044D4CC111F9930CE183F3F38C"}}, "https://[*.]pydantic.dev,*": {"last_modified": "13395157871434512", "setting": {"farbling_token": "31C572373EBBB7D457B4E44B549C8874"}}, "https://[*.]python.org,*": {"last_modified": "13395311888724636", "setting": {"farbling_token": "E1EE0C2D1A8406E9D3B160684F9B26EF"}}, "https://[*.]realpython.com,*": {"last_modified": "13395160708294652", "setting": {"farbling_token": "B65A16CA952D910889E0663F72A76391"}}, "https://[*.]reddit.com,*": {"last_modified": "13395158368395359", "setting": {"farbling_token": "279CE0838C3AA4D1B448B1DA2230C963"}}, "https://[*.]visualstudio.com,*": {"last_modified": "13395158234207511", "setting": {"farbling_token": "2A3DB25F47BF67E17FD67A99CDE0FD3E"}}, "https://[*.]youtube.com,*": {"last_modified": "13395161048568500", "setting": {"farbling_token": "BB1185038118C3824732D2376BD95186"}}}, "braveSpeedreader": {}, "brave_ethereum": {}, "brave_google_sign_in": {}, "brave_localhost_access": {}, "brave_open_ai_chat": {}, "brave_remember_1p_storage": {}, "brave_solana": {}, "brave_webcompat_audio": {}, "brave_webcompat_canvas": {}, "brave_webcompat_device_memory": {}, "brave_webcompat_event_source_pool": {}, "brave_webcompat_font": {}, "brave_webcompat_hardware_concurrency": {}, "brave_webcompat_keyboard": {}, "brave_webcompat_language": {}, "brave_webcompat_media_devices": {}, "brave_webcompat_none": {}, "brave_webcompat_plugins": {}, "brave_webcompat_screen": {}, "brave_webcompat_speech_synthesis": {}, "brave_webcompat_usb_device_serial_number": {}, "brave_webcompat_user_agent": {}, "brave_webcompat_web_sockets_pool": {}, "brave_webcompat_webgl": {}, "brave_webcompat_webgl2": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://consent.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://drive.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://en.wikipedia.org:443,*": {"last_modified": "*****************", "setting": {"client_hints": []}}, "https://gemini.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://photos.google.com:443,*": {"last_modified": "********631304194", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://stackoverflow.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [8, 9, 10, 11, 12, 14, 16, 23]}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://www.youtube.com:443,*": {"last_modified": "13393669363691777", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "cosmeticFiltering": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "fingerprintingV2": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "httpUpgradableResources": {}, "http_allowed": {"https://plus.edilex.fi:443,*": {"last_modified": "13393849380926849", "setting": {"decision_expiration_time": "13395145380926847"}}}, "httpsUpgrades": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://0.0.0.0:8000,*": {"expiration": "13402701584103022", "last_modified": "13394925584103027", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "http://127.0.0.1:38403,*": {"expiration": "13401456239311673", "last_modified": "13393680239311676", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://127.0.0.1:41559,*": {"expiration": "13401452939788802", "last_modified": "13393676939788806", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:34057,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:34363,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 12}}, "https://accounts.google.fi:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://auth.augmentcode.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://auth.openai.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 4}}, "https://chatgpt.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 10}}, "https://code.visualstudio.com:443,*": {"expiration": "13402245832925789", "last_modified": "13394469832925791", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://consent.google.com:443,*": {"expiration": "13402690380588247", "last_modified": "13394914380588249", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://dev.to:443,*": {"expiration": "13401072670270626", "last_modified": "13393296670270629", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://developers.google.com:443,*": {"expiration": "13400933963534121", "last_modified": "13393157963534123", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://docs.anthropic.com:443,*": {"expiration": "13402272119987965", "last_modified": "13394496119987967", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://docs.astral.sh:443,*": {"expiration": "13402934231874150", "last_modified": "13395158231874152", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://docs.google.com:443,*": {"expiration": "13401457319495465", "last_modified": "13393681319495469", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://docs.pydantic.dev:443,*": {"expiration": "13402934053632080", "last_modified": "13395158053632082", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://electerious.medium.com:443,*": {"expiration": "13401072039874963", "last_modified": "13393296039874966", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://esbuild.github.io:443,*": {"expiration": "13401073273107176", "last_modified": "13393297273107178", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://gemini.google.com:443,*": {"expiration": "13402690385370464", "last_modified": "13394914385370466", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://github.com:443,*": {"expiration": "13402769163617122", "last_modified": "13394993163617125", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 8}}, "https://identity.getpostman.com:443,*": {"expiration": "13401542080219502", "last_modified": "13393766080219505", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://invoice.stripe.com:443,*": {"expiration": "13401283193563897", "last_modified": "13393507193563900", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://javascript.plainenglish.io:443,*": {"expiration": "13401073231880575", "last_modified": "13393297231880579", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 4}}, "https://kampus.tukes.fi:443,*": {"expiration": "13401627331918418", "last_modified": "13393851331918421", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://lemonsoftb2c.b2clogin.com:443,*": {"expiration": "13402234445086913", "last_modified": "13394458445086916", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 5}}, "https://login.augmentcode.com:443,*": {"expiration": "13402047934693267", "last_modified": "13394271934693269", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://manikumar.in:443,*": {"expiration": "13401072949493328", "last_modified": "13393296949493330", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://manual.audacityteam.org:443,*": {"expiration": "13401447288961857", "last_modified": "13393671288961859", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 4}}, "https://marketplace.visualstudio.com:443,*": {"expiration": "13400936775099277", "last_modified": "13393160775099279", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://medium.com:443,*": {"expiration": "13402936745503207", "last_modified": "13395160745503210", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://obsidian.md:443,*": {"expiration": "13402885811030141", "last_modified": "13395109811030143", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://oma-login.fonecta.fi:443,*": {"expiration": "13401108623687197", "last_modified": "13393332623687199", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://oma2.fonecta.fi:443,*": {"expiration": "13401108624815572", "last_modified": "13393332624815575", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://online.lemon.fi:443,*": {"expiration": "13402233838378589", "last_modified": "13394457838378591", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 4}}, "https://pay.openai.com:443,*": {"expiration": "13401283195719593", "last_modified": "13393507195719595", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://platform.openai.com:443,*": {"expiration": "13401280661231215", "last_modified": "13393504661231217", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://pulse.appsscript.info:443,*": {"expiration": "13401073168782679", "last_modified": "13393297168782681", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://pypi.org:443,*": {"expiration": "13402766035155004", "last_modified": "13394990035155006", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://realpython.com:443,*": {"expiration": "13402936714537518", "last_modified": "13395160714537521", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://redirect.prod.lemonsoft.io:443,*": {"expiration": "13402051222792190", "last_modified": "13394275222792193", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://scrapbox.io:443,*": {"expiration": "13401073327643704", "last_modified": "13393297327643706", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://script.google.com:443,*": {"expiration": "13401444808592112", "last_modified": "13393668808592113", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://sonicvisualiser.org:443,*": {"expiration": "13401448343549678", "last_modified": "13393672343549681", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://ssl.gstatic.com:443,*": {"expiration": "13400932113012854", "last_modified": "13393156113012858", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://stackoverflow.com:443,*": {"expiration": "13402480648229725", "last_modified": "13394704648229727", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://support.audacityteam.org:443,*": {"expiration": "13401447284501934", "last_modified": "13393671284501937", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://tasker.joaoapps.com:443,*": {"expiration": "13401287809993397", "last_modified": "13393511809993401", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://tukes.edilex.fi:443,*": {"expiration": "13401625779686810", "last_modified": "13393849779686813", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://tukes.fi:443,*": {"expiration": "13401625976158607", "last_modified": "13393849976158611", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.augmentcode.com:443,*": {"expiration": "13402040949944084", "last_modified": "13394264949944087", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.finlex.fi:443,*": {"expiration": "13401627871054832", "last_modified": "13393851871054834", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 6}}, "https://www.geeksforgeeks.org:443,*": {"expiration": "13402766324700018", "last_modified": "13394990324700020", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "13402936758830216", "last_modified": "13395160758830218", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 55}}, "https://www.lansiauto.fi:443,*": {"expiration": "13402040935563132", "last_modified": "13394264935563135", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.midjourney.com:443,*": {"expiration": "13402913652461477", "last_modified": "13395137652461481", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.npmjs.com:443,*": {"expiration": "13401072442221320", "last_modified": "13393296442221323", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://www.opel.fi:443,*": {"expiration": "13402050999540526", "last_modified": "13394274999540529", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.postman.com:443,*": {"expiration": "13401541509974690", "last_modified": "13393765509974692", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.reddit.com:443,*": {"expiration": "13401445513576575", "last_modified": "13393669513576577", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 3}}, "https://www.testo.com:443,*": {"expiration": "13402655849451569", "last_modified": "13394879849451572", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.typescriptlang.org:443,*": {"expiration": "13400947561570170", "last_modified": "13393171561570172", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.yext.com:443,*": {"expiration": "13401108627573952", "last_modified": "13393332627573956", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.youtube.com:443,*": {"expiration": "13401445467406979", "last_modified": "13393669467406982", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 1.3393669371823322e+16, "mediaPlaybacks": 2, "visits": 3}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "referrers": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "shieldsAds": {}, "shieldsCookiesV3": {}, "site_engagement": {"chrome://brave-shields.top-chrome/,*": {"last_modified": "13395399545086354", "setting": {"lastEngagementTime": 1.3395035243792948e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "chrome://history/,*": {"last_modified": "13395399545086198", "setting": {"lastEngagementTime": 1.339464252469832e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "chrome://newtab/,*": {"last_modified": "13395399545086256", "setting": {"lastEngagementTime": 1.3395370376735828e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 30.02354344574139}}, "chrome://sync-internals/,*": {"last_modified": "13395399545086344", "setting": {"lastEngagementTime": 1.339415859416546e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 8.***************}}, "http://0.0.0.0:8000,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395146768916868e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 19.**************}}, "http://127.0.0.1:38403,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394532488243588e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395070201987108e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 7.***************}}, "https://app.augmentcode.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.339482929664754e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://auth.augmentcode.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3394830430173856e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://auth.openai.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395127511917364e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.****************}}, "https://chatgpt.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.33953998346714e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 12.496063692800114, "rawScore": 100.0}}, "https://code.visualstudio.com:443,*": {"last_modified": "13395399545086043", "setting": {"lastEngagementTime": 1.33952781972652e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.6221712693147055}}, "https://consent.google.com:443,*": {"last_modified": "13395399545085845", "setting": {"lastEngagementTime": 1.3395070186225004e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://developers.google.com:443,*": {"last_modified": "13395399545085855", "setting": {"lastEngagementTime": 1.3394352236943072e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://docs.anthropic.com:443,*": {"last_modified": "13395399545085751", "setting": {"lastEngagementTime": 1.3394897720442384e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 6.200804198399999}}, "https://docs.astral.sh:443,*": {"last_modified": "13395399545086334", "setting": {"lastEngagementTime": 1.3395278105653392e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 9.89076213789384}}, "https://docs.augmentcode.com:443,*": {"last_modified": "13395399545085783", "setting": {"lastEngagementTime": 1.3395276853094456e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://docs.google.com:443,*": {"last_modified": "13395399545085865", "setting": {"lastEngagementTime": 1.3394536770864052e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 8.099999999999998}}, "https://docs.pydantic.dev:443,*": {"last_modified": "13395399545086082", "setting": {"lastEngagementTime": 1.339527785335036e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://drive.google.com:443,*": {"last_modified": "13395399545085874", "setting": {"lastEngagementTime": 1.3394595281204884e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.259768756296774}}, "https://esbuild.github.io:443,*": {"last_modified": "13395399545086217", "setting": {"lastEngagementTime": 1.339440147789398e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.099999999999996}}, "https://finlex.fi:443,*": {"last_modified": "13395399545086111", "setting": {"lastEngagementTime": 1.339464370460638e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://gemini.google.com:443,*": {"last_modified": "13395399545085884", "setting": {"lastEngagementTime": 1.3395071235326912e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://github.com:443,*": {"last_modified": "13395399545085826", "setting": {"lastEngagementTime": 1.3395229747746148e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 13.245611435076237}}, "https://help.mapon.com:443,*": {"last_modified": "13395399545085925", "setting": {"lastEngagementTime": 1.339476424390498e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.567295524971575}}, "https://huggingface.co:443,*": {"last_modified": "13395399545085738", "setting": {"lastEngagementTime": 1.3394713714733532e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://javascript.plainenglish.io:443,*": {"last_modified": "13395399545086227", "setting": {"lastEngagementTime": 1.339440144124104e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.699999999999996}}, "https://kampus.tukes.fi:443,*": {"last_modified": "13395399545086169", "setting": {"lastEngagementTime": 1.3394639328859548e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 6.899999999999999}}, "https://lemonsoftb2c.b2clogin.com:443,*": {"last_modified": "13395399545085804", "setting": {"lastEngagementTime": 1.3394833567348104e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.********00000003}}, "https://login.augmentcode.com:443,*": {"last_modified": "13395399545085793", "setting": {"lastEngagementTime": 1.3394830464892388e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://lukianovihor.medium.com:443,*": {"last_modified": "13395399545085934", "setting": {"lastEngagementTime": 1.3395134038877684e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://manikumar.in:443,*": {"last_modified": "13395399545086207", "setting": {"lastEngagementTime": 1.3394401158801e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.099999999999996}}, "https://manual.audacityteam.org:443,*": {"last_modified": "13395399545086266", "setting": {"lastEngagementTime": 1.3394526739152348e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 12.299999999999995}}, "https://marketplace.visualstudio.com:443,*": {"last_modified": "13395399545086053", "setting": {"lastEngagementTime": 1.3395280601313096e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 20.790618213614835}}, "https://netvisor.fi:443,*": {"last_modified": "13395399545086149", "setting": {"lastEngagementTime": 1.3394598554541352e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://numpy.org:443,*": {"last_modified": "13395399545086296", "setting": {"lastEngagementTime": 1.339500218059098e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.1}}, "https://obsidian.md:443,*": {"last_modified": "13395399545086247", "setting": {"lastEngagementTime": 1.3395189892400636e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.5}}, "https://oma2.fonecta.fi:443,*": {"last_modified": "13395399545086121", "setting": {"lastEngagementTime": 1.339443683527754e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://online.lemon.fi:443,*": {"last_modified": "13395399545086140", "setting": {"lastEngagementTime": 1.3394833812159556e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 7.199999999999998}}, "https://packaging.python.org:443,*": {"last_modified": "13395399545086315", "setting": {"lastEngagementTime": 1.3395337620786404e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "https://pay.openai.com:443,*": {"last_modified": "13395399545085973", "setting": {"lastEngagementTime": 1.3394487895726208e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://photos.google.com:443,*": {"last_modified": "13395399545085894", "setting": {"lastEngagementTime": 1.339476419459154e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "https://platform.openai.com:443,*": {"last_modified": "13395399545085984", "setting": {"lastEngagementTime": 1.3394487202337756e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.159999999999997}}, "https://pocketables.com:443,*": {"last_modified": "13395399545085994", "setting": {"lastEngagementTime": 1.3394495201203404e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://pylint.readthedocs.io:443,*": {"last_modified": "13395399545086237", "setting": {"lastEngagementTime": 1.3395136935608148e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "https://pypi.org:443,*": {"last_modified": "13395399545086306", "setting": {"lastEngagementTime": 1.3395133681706796e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.412057608723572}}, "https://rekisterit.tukes.fi:443,*": {"last_modified": "13395399545086178", "setting": {"lastEngagementTime": 1.3394680250413204e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.2}}, "https://stackoverflow.com:443,*": {"last_modified": "13395399545086023", "setting": {"lastEngagementTime": 1.3395000134367076e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.994404014214433}}, "https://support.audacityteam.org:443,*": {"last_modified": "13395399545086276", "setting": {"lastEngagementTime": 1.3394527945491444e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://support.google.com:443,*": {"last_modified": "13395399545085904", "setting": {"lastEngagementTime": 1.339507086370296e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.********00000003}}, "https://tukes.edilex.fi:443,*": {"last_modified": "13395399545086092", "setting": {"lastEngagementTime": 1.339464164146148e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://tukes.fi:443,*": {"last_modified": "13395399545086188", "setting": {"lastEngagementTime": 1.3394639481151248e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 7.499999999999998}}, "https://www.finlex.fi:443,*": {"last_modified": "13395399545086101", "setting": {"lastEngagementTime": 1.3394643914909704e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://www.geeksforgeeks.org:443,*": {"last_modified": "13395399545086286", "setting": {"lastEngagementTime": 1.3395133976124968e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://www.google.com:443,*": {"last_modified": "13395399545085914", "setting": {"lastEngagementTime": 1.339528066702216e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 39.810425714424454}}, "https://www.lansiauto.fi:443,*": {"last_modified": "13395399545086130", "setting": {"lastEngagementTime": 1.3394767093325656e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 12.899999999999995}}, "https://www.midjourney.com:443,*": {"last_modified": "13395399545085944", "setting": {"lastEngagementTime": 1.3395300102893016e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 8.117290669925982}}, "https://www.npmjs.com:443,*": {"last_modified": "13395399545085954", "setting": {"lastEngagementTime": 1.3394400646000916e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 6.350949453266367}}, "https://www.opel.fi:443,*": {"last_modified": "13395399545086159", "setting": {"lastEngagementTime": 1.3394832537877596e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.****************}}, "https://www.postman.com:443,*": {"last_modified": "13395399545086003", "setting": {"lastEngagementTime": 1.3394598842818304e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 6.599999999999999}}, "https://www.reddit.com:443,*": {"last_modified": "13395399545086013", "setting": {"lastEngagementTime": 1.3394526078060452e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.952487105943666}}, "https://www.testo.com:443,*": {"last_modified": "13395399545086033", "setting": {"lastEngagementTime": 1.3395035648181752e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://www.typescriptlang.org:443,*": {"last_modified": "13395399545086325", "setting": {"lastEngagementTime": 1.3394345002035444e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 9.899999999999997}}, "https://www.yext.com:443,*": {"last_modified": "13395399545086063", "setting": {"lastEngagementTime": 1.3394436813031832e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 7.499999999999998}}, "https://www.youtube.com:443,*": {"last_modified": "13395399545086073", "setting": {"lastEngagementTime": 1.3394524912475992e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.939999999999999}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.1.78.102", "creation_time": "13392568065480937", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "SessionEnded", "family_member_role": "not_in_family", "last_engagement_time": "13395399834671401", "last_time_obsolete_http_credentials_removed": 1748096266.900578, "last_time_password_store_metrics_reported": 1750843304.729018, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "chatgpt", "password_hash_data_list": [{"hash": "djExfHfQsX+Qnh6P/lUYd/h+cg==", "is_gaia": "djExp4kSF4WDwCzHnpazBtYnmQ==", "last_signin": 1750440798.734603, "salt_length": "djExS/5IEY1m8WsrT6MrOpZHXV3w51g7c3hjHvNNE3xjWAc=", "username": "djExQ5rdL6J9qCmpyjFbrSc4WGeQVrT5avIQV1lwM357hJQcoQXxyBm28Sl7sQjFpTKa"}, {"hash": "djEx4tqimfCZuH5r/UU9vmDtlA==", "is_gaia": "djExp4kSF4WDwCzHnpazBtYnmQ==", "last_signin": **********.781057, "salt_length": "djExN2dI3evAZ1+h5L8K7oGPnoGctkNBtxd40WIafhAx0mo=", "username": "djExVkifBNcpGHvB++L2qIPhYhZ59XXY9IhpWgaA66sGEZ4="}], "were_old_google_logins_removed": true}, "protection": {"macs": {"brave": {"ad_block": {"developer_mode": "F59142B67502DC56C4D3763941A5F271E22391B134DC3833BE6C25DA10E7804C"}}, "browser": {"show_home_button": "904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"}, "default_search_provider_data": {"template_url_data": "575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"}, "enterprise_signin": {"policy_recovery_token": "7D3124ECAF7E96407EB65EAF5A43B02C7EE5F2D4A9FA38A9F371F9E1B74D6383"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "DC4D7E4DC9BBF07AFD6F75B69526329C8D6FEAB85676AC1E68E69095BA38503F", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "BB3C1593C595C8EF3F5FD95603A1AF9B41F1F929E1D7D1B4EE776FCBFC5BDA8C", "mnojpmjdmbbfmejpflffifhffcmidifd": "B61DEB6BE08E121AA07ED88644A0A797CD08DF21AA95B4BF28A17D0F17219966"}, "ui": {"developer_mode": "55A29C051727FCAC1BDA847CCDE40838B2068CFA589E88819EA9FB17E4BD7320"}}, "google": {"services": {"account_id": "E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486", "last_signed_in_username": "82DB8D884695C643C31778B7B50DBB376848E2F81B5A1AECDA34FD448CECD10D", "last_username": "24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}}, "homepage": "B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E", "homepage_is_newtabpage": "3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119", "media": {"storage_id_salt": "E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"}, "pinned_tabs": "699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8", "prefs": {"preference_reset_time": "95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"}, "safebrowsing": {"incidents_sent": "569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"}, "search_provider_overrides": "1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD", "session": {"restore_on_startup": "F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E", "startup_urls": "8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}}, "safe_browsing": {"external_app_redirect_timestamps": {"com_googleusercontent_apps_44438659992-7kgjeitenc16ssihbtdjbgguch7ju55s": "13393678321706350", "vscode": "13394271950567275"}}, "safebrowsing": {"event_timestamps": {"0": {"12": ["13393507335", "13393509740", "13393668886", "13393673519", "13393677028", "13393848280", "13393848646", "13393848753", "13393848816", "13393848989", "13393849306", "13393854957", "13393855355", "13394636665", "13394648355", "13394648595", "13394658050", "13394661964", "13394661972", "13394882399", "13394906540", "13394983235", "13394983940", "13394984034", "13395006833", "13395007539", "13395008090", "13395009178", "13395058394", "13395059747"]}}, "metrics_last_log_time": "13395401917", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "savefile": {"default_directory": "/home/<USER>/Downloads"}, "selectfile": {"last_directory": "/run/user/1000/gvfs/google-drive:host=jaahdytyspalvelu.fi,user=asiakaspalvelu/0APHWJaeArb6iUk9PVA/1vk3jHe7hxXmtRoOsHqXBZY3wfCW8y1sZ"}, "sessions": {"event_log": [{"crashed": false, "time": "13394935652330953", "type": 0}, {"crashed": true, "time": "13394966588471484", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394966599188645", "type": 5}, {"errored_reading": false, "tab_count": 20, "time": "13394966599238587", "type": 1, "window_count": 1}, {"crashed": true, "time": "13395055897494779", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395056061722540", "type": 5}, {"errored_reading": false, "tab_count": 26, "time": "13395056061760179", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 34, "time": "13395316216164021", "type": 2, "window_count": 4}, {"crashed": false, "time": "13395316874728212", "type": 0}, {"crashed": true, "time": "13395362636434325", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395362639107564", "type": 5}, {"errored_reading": false, "tab_count": 35, "time": "13395362639283788", "type": 1, "window_count": 4}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 34, "time": "13395363102976562", "type": 2, "window_count": 4}, {"crashed": false, "time": "13395399542685067", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395399544990601", "type": 5}, {"errored_reading": false, "tab_count": 34, "time": "13395399545287875", "type": 1, "window_count": 4}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 34, "time": "13395401261496248", "type": 2, "window_count": 4}, {"crashed": false, "time": "13395401917142779", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13395401958932413", "type": 2, "window_count": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"apps": false, "autofill": false, "bookmarks": true, "cached_passphrase_type": 4, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": true, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": true, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": true, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": true, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": true, "send_tab_to_self": true, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": true, "themes": false, "user_consent": true, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": "djExUnI2MXolwyroKKOnOaw6TdQbw91qK0x0G2b55IILu+DMS0gUzCmu8ln4FGs53055CXyYtrRXHkYz0ZR0gaxpRQ=="}, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": false, "feature_status_for_sync_to_signin": 3, "first_full_sync_completed": true, "gaia_id": "B70E8635E7869BDEB3C83878E0E12737FB58703303C08C76F617C2182B55509B", "has_setup_completed": true, "keep_everything_synced": false, "local_device_guids_with_timestamp": [{"cache_guid": "Snf5sKMkWrJfKjjJBeem+A==", "timestamp": 155039}], "passwords": false, "passwords_per_account_pref_migration_done": true, "payments": false, "preferences": false, "reading_list": false, "saved_tab_groups": false, "tabs": false, "themes": false, "transport_data_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": {"sync.bag_of_chips": "", "sync.birthday": "1", "sync.cache_guid": "Snf5sKMkWrJfKjjJBeem+A==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "********"}}, "typed_urls": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 42, "fi": 23}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://github.com/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"helsinki seagulls\",\"jyrki palo<PERSON>\",\"tudum\",\"camilla richardsson\",\"vakava tapaturma juna asemalla\",\"loton oikea rivi\",\"harri he<PERSON>\",\"joonas rinne\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChgIkk4SEwoPVHJlbmRhYXZhdCBoYXV0KAo\\u003d\",\"google:suggestdetail\":[{\"google:entityinfo\":\"CgkvbS8wYnNzbjkSEEtvcmlwYWxsb2pvdWtrdWUyowxkYXRhOmltYWdlL2pwZWc7YmFzZTY0LC85ai80QUFRU2taSlJnQUJBUUFBQVFBQkFBRC8yd0NFQUFrR0J3Z0hCZ2tJQndnS0Nna0xEUllQRFF3TURSc1VGUkFXSUIwaUlpQWRIeDhrS0RRc0pDWXhKeDhmTFQwdE1UVTNPam82SXlzL1JEODRRelE1T2pjQkNnb0tEUXdOR2c4UEdqY2xIeVUzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM04vL0FBQkVJQUVBQVFBTUJJZ0FDRVFFREVRSC94QUFjQUFFQUFnSURBUUFBQUFBQUFBQUFBQUFBQkFVR0J3RUNBd2oveEFBMEVBQUJBd01EQWdJSEJ3VUFBQUFBQUFBQkFnTUVBQkVTQlNFeEJoTWlVUlV5UVdGeGthRUhKRU5TWW9HaUZCWWpRbkwveEFBWkFRQURBUUVCQUFBQUFBQUFBQUFBQUFBQUFRSURCQVgveEFBZUVRQUNBZ01BQXdFQUFBQUFBQUFBQUFBQUFRSVJBeEl4RTBGUkZQL2FBQXdEQVFBQ0VRTVJBRDhBMXhTbEs5UXdGSzk0c0tWTVVSRWpQUFkrdDIwRlFUOFQ3UDNxYzEwNXE3cWNtb3FGanpUSmFOdjVVbTB1c0NxcFV1YnBrNkNuT1ZGY2JSZTNjRmxJdjVaQzQrdFJLZDJBcFNsQUNwbW1lalE2VHF2OVdXN2VGTVpLZHorb2tqYjRiMURwUXdObmRHVE9tMzR3Z2E0UTdwNGRCUWhDMUNLMG9teWU2aFhqQkpOc2xsYUNmYUR0VzRYWTMzVkxFWjB4VzBpeWV5bFBoSGtMaXdGZktrWjkySytsOWhXTGliamNYQkIySUlPeEJHeEI1RmJPNlA4QXRKT254RVE1QWJkam9Ga05QdkZDMlIrVkxoQlN0SGxrVXFISGk1cmx5NG4xRnhrV09vU24yMXlOT1RyTFdxdlpsSTlJUSsxMkcvSUx1RnIrU2tuZzJyRnpvV2tudUtYRERwVHNYSXlySko4eWp1akg0RDZWNWRhOWVTZGNkU3kyaGd0c3J6UXJBTFEycmZkR1EzTzlzeVArUW5rNGk3cUUxNVlXN05sT0tIQ2x2S0pIMXJIOCtlVXJXVFZmRWkxa2dsV3RrL1dkTEVabE0yUEhsc1JsdTlyR1MwcEJ2YTRLY2dDUVJmM2dnN25ZbW9wYytMYytJNUszNVBtZmZ1YVYzeFRTcG1MRlNvbW1haE1TRnhJTXA1QlVFWnRzcUtjaVFBTXJXdVNRS2kxblVIclBUb3ZTVVRTSG84bDkxdElTb0lTR3dtejJac3ZJMzIvVHp6U2sydUlhS0JYU1d2SW53NEwybkxha1RDc01KVzRnWllDNjc3K0d3STV0WGFMMGxxMGh1UXNpS3gySks0cFRKa3BiSzNVQzZrSnY2eHQ1VmZ5K3VWSzFXUnFVWHB6c3pYWTd0bjNKQldRaHpIeFk0QVdHUHY1TnlhNFQ5b21yQ1ZKZmphVEZiY2VVcVY0UzZjU1d3bFNpQW9BcHNrSGNXdUw4MWx2T3ZRNlJVZjJUcXhaUzZsM1RsSVUydzRDSmlQVmVObXp2K1k4VlhyNmQxVk1uVW82WW9XNXBpY3BtRHFDR2hibm5mZzhYdFZxeDFYT0duUm9mb3FPNTJHWW9XNkE0Rk9zeGxaTjNzcXdBUEtnQlV0WFYwWnh5WTVJNlVTQnFqVGdrTFptTFFwNUpYbXBWeWczc1Ird3ZUM2t1aFNNU1hCbU54VVMxeEpDWXJucVBscFFiVnZiWlZyYzE0Vm5FcnJEU1gra1pXaXNSSnNjNEZFWnNxQzBvVDNjd0N2SUVnSjJzcEozOXRZUFdrVzMxRXNVcFNxQWxMbnlGa0ZTZ2Z1NGpjZmhqZ1YxVE1lU29LR0Yrd1dQVi93QkNMZk8zdHFQU284Y09VUFprbHFkSVp2MjFnWlIxUnp0K0dlUlhCbVBGTElKVC9oYVUwandqWktzcmorUitkUjZVZU9IYURaL1JTbEtzUi8vWjoRSGVsc2lua2kgU2VhZ3VsbHNKByM0MjQyNDJSPWdzX3NzcD1lSnpqNHRUUDFUZElLaTdPc3pSZzlCTE1TTTBwenN6THpsUW9UazFNTDgzSktRWUFsU1lLWVFwBw\\u003d\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"google:entityinfo\":\"Cg0vZy8xMWc5bF92a2Y5EiJTdW9tYWxhaW5lbiBrZXNraW1hdGtvamVuIGp1b2tzaWphMtcOZGF0YTppbWFnZS9qcGVnO2Jhc2U2NCwvOWovNEFBUVNrWkpSZ0FCQVFBQUFRQUJBQUQvMndDRUFBa0dCd2dIQmdrSUJ3Z0tDZ2tMRFJZUERRd01EUnNVRlJBV0lCMGlJaUFkSHg4a0tEUXNKQ1l4Sng4ZkxUMHRNVFUzT2pvNkl5cy9SRDg0UXpRNU9qY0JDZ29LRFF3TkdnOFBHamNsSHlVM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOLy9BQUJFSUFFQUFRQU1CSWdBQ0VRRURFUUgveEFBYUFBQUNBd0VCQUFBQUFBQUFBQUFBQUFBRUJRTUdCd0lCLzhRQU5SQUFBZ0VEQWdJR0J3Z0RBQUFBQUFBQUFRSURBQVFSRWlFRk1RWVRRVkZoa1JRaU1sSnhnY0VWUW9LaHNkSGg4RE5pa3YvRUFCa0JBQUlEQVFBQUFBQUFBQUFBQUFBQUFBSURBUVFGQVAvRUFDY1JBQUlDQVFJRUJnTUFBQUFBQUFBQUFBRUNBQU1SQkNFRkVqRkJFeUpSY2FIeFljSHcvOW9BREFNQkFBSVJBeEVBUHdEUUk3cUp2ZlA0YUlpbVZnUFZkYys5MlV1aVM1ajVENTFPa3o4amdId05UN1NjK3NONnZWY1J4aVBJa1Z6cTFjaXVuYkdPM0o4cW1XR1pSZ2hpS0dTNEtUMllKQVBYRUhWM0dOdnFCUkhHYmh2c2k5TVdXYnFYQTAvQ2dMTUlZQU1HaDRqWlhWeTl2YTNzRWt5RExJc2dKSGxSSXVuWGJUa2R0WnAwUXVldDZRMmtVTU5zQjYyZXBJMkdEMllHMkswa3dzRGdTS255cVZJWWJ3clVOYllCazBseUF1UmpQK29vV1NSeXVXT0JYRXRucTM5SXlmQ28xczRnZjhyc2ZHcHdvaThzWUFMbjJWamt3U045NkpXNVJlUUJJSFBIT3FURjBuMG9wa3NHeWVZamwxZlNncnpqOSszRVlacldHUklRbzFSNjhnNEp6bjViVVBNazdsZnJORG1uRENGdEtxUlBIK2JBZm9hOXVlTjJyMjhxV3pDVEdVT2M2VFZIdStrZHhOWlN0SmFpQ0lnS0JuVTJyK2pPYVgyZkZibXhzdXFtaGsxRmlScTU3MDJoVnVzNUIwZ2FoalJUNGg2N1J2MFY2dmczRVo3aThDNkNwalZ0UXlneUQ5QlY3Z3ZMUzZqMXdTYWdEdVF4MnJIZUpjWGxKWlJFNHlxbkdPOEEvV21kbnhLNjRUTGR3cEtzcnkyK3dBSzc5dVBFYmp5NVp4VE5UUU5NcTc1ekIwdDc2eG5KWEdNWStaZmVEOFloNHFMbDRVZFVpazBEUGI0K1dLSXVMNkszQU1vSUI1YmsxbFhSbmpVMWh4Um1kbFNBdG1jeUhHQnVQclZvNGwwcTRjU3c2OWVyWGRXS0FrblBjVDRWU3NzUEw1Umt5M1hWbHZNY0NVdFpaYzQ3Zm5Sb3Q3NUVMdmJ6cW9HU1RHd0FwWFozc3R0SVhoMEhLNElrQllma1IzVXcrMlo1UmlTM3RHK01aL2VrSEhZUjQ0Vmtidko0a2U0aGxpTERVeWtxQnYyVkJ4R1J5NE0ycFcwREdySXFXTGlrNk1EREJaUnVPVExBY2o0RU5VRi9kUlN5dEpkem9HOE9aL2ZzOHEwK0ZLeHVMQWJEOS9VemVNNmRLTk9xbHNudGorL01oTENlL3dBaHk0NnpVdU44Z0hiYnNwaU90bmxpQ0tYYzUwNlJrbjRVa3M3OVVuaUVCQkRNQnFJempmblRMMGg3YTU5S2huWHJRUzJWVTg4ZVg5N2FMaXlzempIcHRJNFZTb1F1VGcrMGxtdUlWakxQUGI1QklLZGN1b0h4WG5Ralh0dmpQV0RBN2dhc3RoeHl6NHBONk5jaU9XNDI5cktPMjJUcElBemo2VXU2WFhMOEJlQXhSR2FDWUhCYzRLc093a2ZIOWF5OUcybXZ0OEIrWlg5RGo0UDFMdCtudXBYeEZiS3lzb3lqN3JlZEVSc3Z1ay9pL2lnRVpNKzAvd0R6L05FUnZHQjkrb3hOa05HRVRKclVZMDcrMFRzUHlwY2VIeUx4RlZtSWtpVDF0V1J2M2Rwb3VCdFJab2xZc2k2aHFiYnU3dkd1SW1FWUljTXo1MzliK0tmVnFiS2xaRTd4TjJtcnVzVjM3UUhoTm82WEN2S3k0ajNBQnlHMkkrV0tjU092dXI1bWhvRldGbkpVTUNmVkJQSVY2OHk5a1MrWi9laHYxRDNzR2FGcHFFb1FxdmVGY01VcnhDSzVqZ2prTnNldVlPd1VCUjI1SjI1N2VPS3NmR3JqZ25TSGdyUXkza05zNDlkRE80Vm8ySGVDZHh6RzNmVkxlUUg3aTdmR2hwbVZnVmRGUHh6Vk95am1zVzFUaGw2R0crQ0NwR3huLzlrPToMSm9vbmFzIFJpbm5lSgcjNzUyNDIyUjtnc19zc3A9ZUp6ajR0VlAxemMwVExmTWlTX0xUck0wWVBUaXljclB6MHNzVmlqS3pNdExCUUNGSUFsM3AGcAc\\u003d\",\"zl\":10002}],\"google:suggesteventid\":\"-2634259844034384253\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"ENTITY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"ENTITY\"]}]", "cachedresults_with_url": {"https://www.google.com/search?q=Mypy+Type+Checker&oq=Mypy+Type+Checker&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIGCAEQRRg8MgYIAhAuGEDSAQc2MDVqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8": ")]}'\n[\"\",[\"mypy type ignore\",\"mypy type checking\",\"mypy incompatible types in assignment\",\"mypy self type\",\"mypy static type checking\",\"mypy custom type\"],[\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChcIwLgCEhEKD1JlY2VudCBzZWFyY2hlcwohCJBOEhwKGlJlbGF0ZWQgdG8gcmVjZW50IHNlYXJjaGVz\",\"google:suggestdetail\":[{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000}],\"google:suggesteventid\":\"5057697090538621838\",\"google:suggestrelevance\":[601,600,553,552,551,550],\"google:suggestsubtypes\":[[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308],[512,650,273,524,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}}