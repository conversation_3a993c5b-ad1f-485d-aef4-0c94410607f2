{"accessibility": {"captions": {"headless_caption_enabled": false}}, "alternate_error_pages": {"backup": false}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "13395315097715568"}, "brave": {"accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "AltGr", "Alt"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "branded_wallpaper_notification_dismissed": true, "brave_ads": {"notification_ads": [], "should_allow_ads_subdivision_targeting": false, "state": {"has_migrated": {"client": {"v7": true}, "confirmations": {"v8": true}, "v2": true}}}, "brave_search": {"last-used-ntp-search-engine": "search.brave.com"}, "default_accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "Alt", "Alt", "AltGr"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "default_private_search_provider_data": {"alternate_urls": [], "contextual_search_url": "", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://cdn.search.brave.com/serp/favicon.ico", "featured_by_policy": false, "id": "2", "image_search_branding_label": "", "image_translate_source_language_param_key": "", "image_translate_target_language_param_key": "", "image_translate_url": "", "image_url": "", "image_url_post_params": "", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": ":br", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": false, "prefetch_likely_navigations": false, "prepopulate_id": 550, "safe_for_autoreplace": true, "search_intent_params": [], "search_url_post_params": "", "short_name": "Brave", "starter_pack_id": 0, "suggestions_url": "https://search.brave.com/api/suggest?q={searchTerms}&rich=true&source=desktop", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "https://search.brave.com/search?q={searchTerms}&source=desktop", "usage_count": 0}, "default_private_search_provider_guid": "485bf7d3-0215-45af-87dc-************", "enable_media_router_on_restart": true, "enable_window_closing_confirm": true, "migrated_search_default_in_jp": true, "rewards": {"notifications": "{\"displayed\":[],\"notifications\":[]}", "scheduled_captcha": {"failed_attempts": 0, "id": "", "paused": false, "payment_id": ""}}, "search": {"default_version": 32}, "shields_settings_version": 4, "stats": {"ads_blocked": "127", "bandwidth_saved_bytes": "5902222", "daily_saving_predictions_bytes": [{"day": **********.0, "value": 1175168.0}, {"day": **********.0, "value": 157654.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 552181.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "today": {"p3a_total_card_views": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_card_visits": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_sidebar_filter_usages": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "wallet": {"aurora_mainnet_migrated": true, "custom_networks": {"goerli_migrated": true}, "eip1559_chains_migrated": true, "is_compressed_nft_migrated": true, "is_spl_token_program_migrated": true, "keyrings": {}, "last_transaction_sent_time_dict": {}}, "webtorrent_enabled": false, "weekly_storage": {"Brave": {"P2A": {"new_tab_page_ad": {"opportunities": [{"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 3.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}]}}}, "search_count": [{"day": **********.0, "value": 2.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}}, "brave_shields": {"p3a_ads_allow_domain_count": 0, "p3a_ads_standard_domain_count": 0, "p3a_ads_strict_domain_count": 0, "p3a_first_reported_revision": 3, "p3a_fp_allow_domain_count": 0, "p3a_fp_standard_domain_count": 0, "p3a_fp_strict_domain_count": 0}, "brave_sync_v2": {"account_deleted_notice_pending": false, "reset_devices_progress_token_time": "*****************", "seed": "djExnE88VoEjemn66hYkijwcvlJUyml3ToBvkIQKujBK+fp9I1DI4QiZsILkVwza1UhQoHw1I20usWNaeI1Fys994LJWKvXQEsDVU/H4NxNDH3+mostDEaRfG+BB0EF2d1YeasZhAZLzK2YxS8nME9TB9k2Yh7fp88ScFOp3o7wvlGcG1QfX5IkzHCdoP+svatgb"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1090, "left": 1930, "maximized": true, "right": 3850, "top": 10, "work_area_bottom": 1080, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17993, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "78", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "download_bubble": {"partial_view_impressions": 6}, "enterprise_profile_guid": "5d30a7fa-87f9-4092-9dbb-3c9423209dc0", "ephemeral_storage": {"first_party_storage_origins_to_cleanup": []}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "************", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Brave.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Brave Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/opt/brave.com/brave/resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://brave-resources/*", "chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://brave-resources chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://brave-resources/", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/opt/brave.com/brave/resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnojpmjdmbbfmejpflffifhffcmidifd": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["*://*/*", "<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"all_frames": true, "js": ["out/webstore.bundle.js"], "matches": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"], "run_at": "document_start"}], "content_security_policy": "default-src 'self'; font-src 'self' data:; script-src 'self' 'wasm-eval'; style-src 'unsafe-inline'; img-src 'self' data: chrome://favicon/; connect-src * data: blob: filesystem:;", "default_locale": "en_US", "description": "__MSG_appName__", "icons": {"128": "assets/img/icon-128.png", "16": "assets/img/icon-16.png", "256": "assets/img/icon-256.png", "32": "assets/img/icon-32.png", "48": "assets/img/icon-48.png", "64": "assets/img/icon-64.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAupOLMy5Fd4dCSOtjcApsAQOnuBdTs+OvBVt/3P93noIrf068x0xXkvxbn+fpigcqfNamiJ5CjGyfx9zAIs7zcHwbxjOw0Uih4SllfgtK+svNTeE0r5atMWE0xR489BvsqNuPSxYJUmW28JqhaSZ4SabYrRx114KcU6ko7hkjyPkjQa3P+chStJjIKYgu5tWBiMJp5QVLelKoM+xkY6S7efvJ8AfajxCViLGyDQPDviGr2D0VvIBob0D1ZmAoTvYOWafcNCaqaejPDybFtuLFX3pZBqfyOCyyzGhucyCmfBXJALKbhjRAqN5glNsUmGhhPK87TuGATQfVuZtenMvXMQIDAQAB", "manifest_version": 2, "name": "Brave", "permissions": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "webNavigation", "webRequest", "*://*/*", "chrome://favicon/*", "webDiscovery", "webRequestBlocking", "unlimitedStorage", "<all_urls>"], "version": "1.0.0"}, "path": "/opt/brave.com/brave/resources/brave_extension", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gcm": {"product_category_for_subtypes": "com.brave.linux"}, "google": {"services": {"signin_scoped_device_id": "9e84cb87-eb9b-41f9-b957-41ee05243a0d"}}, "https_upgrade_navigations": {"2025-06-12": 70, "2025-06-13": 10, "2025-06-15": 30, "2025-06-16": 10, "2025-06-17": 60, "2025-06-18": 10, "2025-06-20": 10, "2025-06-21": 10, "2025-06-23": 10, "2025-06-25": 10}, "in_product_help": {"new_badge": {"ComposeNudge": {"feature_enabled_time": "13392568077390402", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "13392568077390408", "show_count": 0, "used_count": 0}}, "policy_last_heavyweight_promo_time": "13394637696319197", "recent_session_enabled_time": "13392568077390196", "recent_session_start_times": ["13395308330407303", "13395150685289236", "13394966611187770", "13394889207347430", "13394700840947666", "13394622698642581", "13394584964508407", "13394548336145264", "13394490335579322", "13394456719650712", "13394261172368259", "13394234316002621", "13394190321601624", "13393851957878621", "13393350062568824", "13393291930816054", "13393239184826553", "13393154328765827", "13392982690224288", "13392801899721181"], "session_last_active_time": "13395317021820737", "session_start_time": "13395308330407303", "snoozed_feature": {"IPH_DiscardRing": {"first_show_time": "13393240805248904", "is_dismissed": true, "last_dismissed_by": 0, "last_show_time": "13394637696319202", "last_snooze_time": "0", "promo_index": 0, "show_count": 3, "shown_for_apps": [], "snooze_count": 0}}}, "intl": {"accept_languages": "fi,en-US,en", "selected_languages": "fi,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "language_model_counters": {"en": 239, "fi": 21}, "media": {"device_id_salt": "D1832E97E94DDF62109793E3226B4EDD", "engagement": {"schema_version": 5}}, "media_router": {"enable_media_router": true, "receiver_id_hash_token": "ZkHQDNrraJ9Y7NxtIGsGmwMTLtfi+Z0h39vAeLjzIlp0iynmHNQA2gMPhRwawKe/G/AQz6Hr19eQXFVhZOgkwQ=="}, "ntp": {"num_personal_suggestions": 2}, "omnibox": {"shown_count_history_scope_promo": 3}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "pinned_tabs": [], "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false, "m1": {"ad_measurement_enabled": false, "fledge_enabled": false, "topics_enabled": false}}, "profile": {"avatar_index": "63", "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://claude.ai:443,*": {"last_modified": "*****************", "setting": {"https://claude.ai/": {"couldShowBannerEvents": 1.3394190815791664e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13394190815791553"}}}}, "https://community.brave.com:443,*": {"last_modified": "13392582199338699", "setting": {"https://community.brave.com/": {"couldShowBannerEvents": 1.3392582199338694e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13392582199338551"}}}}, "https://docs.github.com:443,*": {"last_modified": "13394637426713704", "setting": {"https://docs.github.com/": {"couldShowBannerEvents": 1.3394239132162038e+16, "next_install_text_animation": {"delay": "345600000000", "last_shown": "13394637426713698"}}}}, "https://github.com:443,*": {"last_modified": "13394910103784166", "setting": {"https://github.com/": {"couldShowBannerEvents": 1.3392568766198758e+16, "next_install_text_animation": {"delay": "1382400000000", "last_shown": "13394910103784160"}}}}, "https://regex101.com:443,*": {"last_modified": "13394968118493454", "setting": {"https://regex101.com/": {"couldShowBannerEvents": 1.3394968118493452e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13394968118493333"}}}}, "https://www.reddit.com:443,*": {"last_modified": "13394639939872537", "setting": {"https://www.reddit.com/": {"couldShowBannerEvents": 1.3392582220927752e+16, "next_install_text_animation": {"delay": "345600000000", "last_shown": "13394639939872525"}}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "braveShields": {}, "braveShieldsMetadata": {"https://[*.]github.com,*": {"last_modified": "13395158204617906", "setting": {"farbling_token": "A7A58E2D4EC8C82F4E0B4D99B100A912"}}, "https://[*.]regex101.com,*": {"last_modified": "13395316883287020", "setting": {"farbling_token": "9965408D0C1D3DE7B0043181FB08C8ED"}}, "https://[*.]vscode.dev,*": {"last_modified": "13395316883417473", "setting": {"farbling_token": "245F3A9D3D2DDEFB9A0669B618F53B8F"}}}, "braveSpeedreader": {}, "brave_ethereum": {}, "brave_google_sign_in": {}, "brave_localhost_access": {}, "brave_open_ai_chat": {}, "brave_remember_1p_storage": {}, "brave_solana": {}, "brave_webcompat_audio": {}, "brave_webcompat_canvas": {}, "brave_webcompat_device_memory": {}, "brave_webcompat_event_source_pool": {}, "brave_webcompat_font": {}, "brave_webcompat_hardware_concurrency": {}, "brave_webcompat_keyboard": {}, "brave_webcompat_language": {}, "brave_webcompat_media_devices": {}, "brave_webcompat_none": {}, "brave_webcompat_plugins": {}, "brave_webcompat_screen": {}, "brave_webcompat_speech_synthesis": {}, "brave_webcompat_usb_device_serial_number": {}, "brave_webcompat_user_agent": {}, "brave_webcompat_web_sockets_pool": {}, "brave_webcompat_webgl": {}, "brave_webcompat_webgl2": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.google.com:443,*": {"last_modified": "13394968114842649", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "cosmeticFiltering": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "fingerprintingV2": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "httpUpgradableResources": {}, "http_allowed": {}, "httpsUpgrades": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://0.0.0.0:8000,*": {"expiration": "13402011186197707", "last_modified": "13394235186197711", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 9}}, "https://claude.ai:443,*": {"expiration": "13401966904024749", "last_modified": "13394190904024752", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://codapi.org:443,*": {"expiration": "13402744050879975", "last_modified": "13394968050879977", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://community.brave.com:443,*": {"expiration": "13400358215673400", "last_modified": "13392582215673402", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://docs.github.com:443,*": {"expiration": "13402415388516902", "last_modified": "13394639388516904", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://github.com:443,*": {"expiration": "13402743781850139", "last_modified": "13394967781850141", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 8}}, "https://login.circle.so:443,*": {"expiration": "13400361669374743", "last_modified": "13392585669374746", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://support.brave.com:443,*": {"expiration": "13400357596505647", "last_modified": "13392581596505648", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "13402744117909228", "last_modified": "13394968117909230", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 10}}, "https://www.reddit.com:443,*": {"expiration": "13400358281007449", "last_modified": "13392582281007451", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.sqlalchemy.org:443,*": {"expiration": "13402428607028392", "last_modified": "13394652607028394", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "referrers": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "shieldsAds": {}, "shieldsCookiesV3": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13395310845808744", "setting": {"lastEngagementTime": 1.3395280993360356e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 23.347113327079676}}, "chrome://settings/,*": {"last_modified": "13395310845808799", "setting": {"lastEngagementTime": 1.3394616122878732e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://0.0.0.0:8000,*": {"last_modified": "13395310845808451", "setting": {"lastEngagementTime": 1.3395219897005008e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 12.289330319767952}}, "https://claude.ai:443,*": {"last_modified": "13395310845808542", "setting": {"lastEngagementTime": 1.33948218069096e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "https://codapi.org:443,*": {"last_modified": "13395310845808763", "setting": {"lastEngagementTime": 1.3395220699482128e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://community.brave.com:443,*": {"last_modified": "13395310845808579", "setting": {"lastEngagementTime": 1.3394619745050524e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.9000000000000004}}, "https://docs.github.com:443,*": {"last_modified": "13395310845808620", "setting": {"lastEngagementTime": 1.3395087408196624e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 8.224036223824806}}, "https://docs.sqlalchemy.org:443,*": {"last_modified": "13395310845808781", "setting": {"lastEngagementTime": 1.3395103666315244e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://github.com:443,*": {"last_modified": "13395317075687209", "setting": {"lastEngagementTime": 1.339531707568718e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 12.299999999999995, "rawScore": 94.44913065410458}}, "https://medium.com:443,*": {"last_modified": "13395310845808677", "setting": {"lastEngagementTime": 1.339473535231638e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 9.899999999999997}}, "https://prettier.io:443,*": {"last_modified": "13395310845808728", "setting": {"lastEngagementTime": 1.3394733891319564e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 7.799999999999998}}, "https://regex101.com:443,*": {"last_modified": "13395310845808709", "setting": {"lastEngagementTime": 1.3395223372276056e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 10.499999999999996}}, "https://support.brave.com:443,*": {"last_modified": "13395310845808600", "setting": {"lastEngagementTime": 1.3394618884249548e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 10.499999999999996}}, "https://www.google.com:443,*": {"last_modified": "13395310845808661", "setting": {"lastEngagementTime": 1.3395220768007072e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 13.935896572856333}}, "https://www.reddit.com:443,*": {"last_modified": "13395310845808694", "setting": {"lastEngagementTime": 1.3395088529200888e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.569850545178754}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.1.78.102", "creation_time": "13392568077353020", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13395317075687181", "last_time_obsolete_http_credentials_removed": 1748095225.050938, "last_time_password_store_metrics_reported": **********.738374, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "github", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"brave": {"ad_block": {"developer_mode": "F59142B67502DC56C4D3763941A5F271E22391B134DC3833BE6C25DA10E7804C"}}, "browser": {"show_home_button": "904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"}, "default_search_provider_data": {"template_url_data": "575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"}, "enterprise_signin": {"policy_recovery_token": "7D3124ECAF7E96407EB65EAF5A43B02C7EE5F2D4A9FA38A9F371F9E1B74D6383"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "035A745237B4FD16CBA6DFF02ABEDF018982E0AD6A32037D555979A44B8FAFA7", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "B1BB54B03B8E2DAF9DDCEFD5F492A7B81CBBFEC7DF46D697C2696306EA1F3AB1", "mnojpmjdmbbfmejpflffifhffcmidifd": "5B5C7C2BC9D984F1638B258A9F1BD1FCF30532274D6361B13B990B62C0F31E32"}, "ui": {"developer_mode": "55A29C051727FCAC1BDA847CCDE40838B2068CFA589E88819EA9FB17E4BD7320"}}, "google": {"services": {"account_id": "E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486", "last_signed_in_username": "82DB8D884695C643C31778B7B50DBB376848E2F81B5A1AECDA34FD448CECD10D", "last_username": "24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}}, "homepage": "B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E", "homepage_is_newtabpage": "3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119", "media": {"storage_id_salt": "E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"}, "pinned_tabs": "699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8", "prefs": {"preference_reset_time": "95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"}, "safebrowsing": {"incidents_sent": "569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"}, "search_provider_overrides": "1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD", "session": {"restore_on_startup": "F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E", "startup_urls": "8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}}, "safe_browsing": {"external_app_redirect_timestamps": {"vscode": "13394237954346863"}}, "safebrowsing": {"event_timestamps": {"0": {"12": ["13394212630", "13394213009", "13394213483", "13394242380", "13394263322", "13394268975", "13394457820", "13394469204", "13394471562", "13394474281", "13394491511", "13394496110", "13394622732", "13394633416", "13394637704", "13394640057", "13394645039", "13394647976", "13394655107", "13394700931", "13394914454", "13395310881"]}}, "metrics_last_log_time": "13395315097", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "savefile": {"default_directory": "/home/<USER>/Downloads"}, "sessions": {"event_log": [{"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13394662199120645", "type": 2, "window_count": 0}, {"crashed": false, "time": "13394700840936303", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394700840939127", "type": 5}, {"errored_reading": false, "tab_count": 17, "time": "13394700841142991", "type": 1, "window_count": 4}, {"crashed": true, "time": "13394878867872566", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394889209311453", "type": 5}, {"errored_reading": false, "tab_count": 17, "time": "13394889209419758", "type": 1, "window_count": 3}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 16, "time": "13394932826288126", "type": 2, "window_count": 3}, {"crashed": false, "time": "13394935652415392", "type": 0}, {"crashed": true, "time": "13394966588549993", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394966613814957", "type": 5}, {"errored_reading": false, "tab_count": 17, "time": "13394966613935167", "type": 1, "window_count": 3}, {"crashed": true, "time": "13395055897527649", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395150687703980", "type": 5}, {"errored_reading": false, "tab_count": 19, "time": "13395150687733669", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 18, "time": "13395316216164097", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395316874737654", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395316879134518", "type": 5}, {"errored_reading": false, "tab_count": 18, "time": "13395316879251648", "type": 1, "window_count": 1}], "session_data_status": 1}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"apps": false, "autofill": false, "bookmarks": true, "cached_passphrase_type": 4, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": true, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": true, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": true, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": true, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": true, "send_tab_to_self": true, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": true, "themes": false, "user_consent": true, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": "djExUnI2MXolwyroKKOnOaw6TdQbw91qK0x0G2b55IILu+DMS0gUzCmu8ln4FGs53055CXyYtrRXHkYz0ZR0gaxpRQ=="}, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": false, "feature_status_for_sync_to_signin": 3, "first_full_sync_completed": true, "gaia_id": "B70E8635E7869BDEB3C83878E0E12737FB58703303C08C76F617C2182B55509B", "has_setup_completed": true, "keep_everything_synced": false, "local_device_guids_with_timestamp": [{"cache_guid": "mAJA7VP5mYR1mw1qM2GTHw==", "timestamp": 155038}], "passwords": false, "passwords_per_account_pref_migration_done": true, "payments": false, "preferences": false, "reading_list": false, "saved_tab_groups": false, "tabs": false, "themes": false, "transport_data_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": {"sync.bag_of_chips": "", "sync.birthday": "1", "sync.cache_guid": "mAJA7VP5mYR1mw1qM2GTHw==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "********"}}, "typed_urls": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 35, "fi": 2}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://github.com/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}, "https://regex101.com/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"auto onnettomuus sotkamo\",\"16 miljardia salasanaa\",\"teemu kivihalme\",\"stora enso osake\",\"indiana jones elokuvat\",\"kesäpäivänse<PERSON>us\",\"ali ja ava\",\"aleksi saarela\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChgIkk4SEwoPVHJlbmRhYXZhdCBoYXV0KAo\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\\u003d\",\"zl\":10002}],\"google:suggesteventid\":\"-8908033898506759737\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"ENTITY\"]}]", "cachedresults_with_url": {"https://www.google.com/search?q=regex101&oq=regex&gs_lcrp=EgZjaHJvbWUqBwgBEAAYgAQyCQgAEEUYORiABDIHCAEQABiABDIHCAIQABiABDIHCAMQABiABDIHCAQQABiABDIGCAUQRRg9MgYIBhBFGD0yBggHEC4YQNIBCDMzNzhqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8": ")]}'\n[\"\",[\"python regex\",\"regex generator\",\"regex tester\",\"regex not\",\"regex or\",\"javascript regex\",\"python re\",\"regex tutorial\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIwLgCEhQKElZpaW1laXNpbW3DpHQgaGF1dAomCJBOEiEKH0xpaXR0eXkgdmlpbWVhaWthaXNpaW4gaGFrdWloaW4\\u003d\",\"google:suggestdetail\":[{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000}],\"google:suggesteventid\":\"-8609269828677065439\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}}