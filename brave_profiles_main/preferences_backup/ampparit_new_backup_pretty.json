{"accessibility": {"captions": {"headless_caption_enabled": false}}, "alternate_error_pages": {"backup": false}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "13395279722610976"}, "brave": {"accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "AltGr", "Alt"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "brave_ads": {"notification_ads": [], "should_allow_ads_subdivision_targeting": false, "state": {"has_migrated": {"client": {"v7": true}, "confirmations": {"v8": true}, "v2": true}}}, "brave_search": {"last-used-ntp-search-engine": "search.brave.com"}, "default_accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "Alt", "Alt", "AltGr"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "default_private_search_provider_data": {"alternate_urls": [], "contextual_search_url": "", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://cdn.search.brave.com/serp/favicon.ico", "featured_by_policy": false, "id": "0", "image_search_branding_label": "", "image_translate_source_language_param_key": "", "image_translate_target_language_param_key": "", "image_translate_url": "", "image_url": "", "image_url_post_params": "", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": ":br", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": false, "prefetch_likely_navigations": false, "prepopulate_id": 550, "safe_for_autoreplace": true, "search_intent_params": [], "search_url_post_params": "", "short_name": "Brave", "starter_pack_id": 0, "suggestions_url": "https://search.brave.com/api/suggest?q={searchTerms}&rich=true&source=desktop", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "https://search.brave.com/search?q={searchTerms}&source=desktop", "usage_count": 0}, "default_private_search_provider_guid": "485bf7d3-0215-45af-87dc-************", "enable_media_router_on_restart": true, "enable_window_closing_confirm": true, "migrated_search_default_in_jp": true, "rewards": {"notifications": "{\"displayed\":[],\"notifications\":[]}", "scheduled_captcha": {"failed_attempts": 0, "id": "", "paused": false, "payment_id": ""}}, "search": {"default_version": 32}, "shields_settings_version": 4, "stats": {"ads_blocked": "1743", "bandwidth_saved_bytes": "42024371", "daily_saving_predictions_bytes": [{"day": **********.0, "value": 9566041.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 9087448.0}, {"day": **********.0, "value": 0.0}]}, "today": {"p3a_total_card_views": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_card_visits": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_sidebar_filter_usages": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "wallet": {"aurora_mainnet_migrated": true, "custom_networks": {"goerli_migrated": true}, "eip1559_chains_migrated": true, "is_compressed_nft_migrated": true, "is_spl_token_program_migrated": true, "keyrings": {}, "last_transaction_sent_time_dict": {}}, "webtorrent_enabled": false, "weekly_storage": {"Brave": {"P2A": {"new_tab_page_ad": {"opportunities": [{"day": **********.0, "value": 2.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 3.0}, {"day": **********.0, "value": 0.0}]}}}, "search_count": [{"day": **********.0, "value": 1.0}]}}, "brave_shields": {"p3a_ads_allow_domain_count": 0, "p3a_ads_standard_domain_count": 0, "p3a_ads_strict_domain_count": 0, "p3a_first_reported_revision": 3, "p3a_fp_allow_domain_count": 0, "p3a_fp_standard_domain_count": 0, "p3a_fp_strict_domain_count": 0}, "brave_sync_v2": {"account_deleted_notice_pending": false, "reset_devices_progress_token_time": "*****************", "seed": "djExnE88VoEjemn66hYkijwcvlJUyml3ToBvkIQKujBK+fp9I1DI4QiZsILkVwza1UhQoHw1I20usWNaeI1Fys994LJWKvXQEsDVU/H4NxNDH3+mostDEaRfG+BB0EF2d1YeasZhAZLzK2YxS8nME9TB9k2Yh7fp88ScFOp3o7wvlGcG1QfX5IkzHCdoP+svatgb"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1080, "left": 1920, "maximized": true, "right": 3840, "top": 0, "work_area_bottom": 1080, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17993, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "b9da2785-f469-455e-919e-63e7a9f13b73", "ephemeral_storage": {"first_party_storage_origins_to_cleanup": []}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "************", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Brave.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Brave Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/opt/brave.com/brave/resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://brave-resources/*", "chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://brave-resources chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://brave-resources/", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/opt/brave.com/brave/resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnojpmjdmbbfmejpflffifhffcmidifd": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["*://*/*", "<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"all_frames": true, "js": ["out/webstore.bundle.js"], "matches": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"], "run_at": "document_start"}], "content_security_policy": "default-src 'self'; font-src 'self' data:; script-src 'self' 'wasm-eval'; style-src 'unsafe-inline'; img-src 'self' data: chrome://favicon/; connect-src * data: blob: filesystem:;", "default_locale": "en_US", "description": "__MSG_appName__", "icons": {"128": "assets/img/icon-128.png", "16": "assets/img/icon-16.png", "256": "assets/img/icon-256.png", "32": "assets/img/icon-32.png", "48": "assets/img/icon-48.png", "64": "assets/img/icon-64.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAupOLMy5Fd4dCSOtjcApsAQOnuBdTs+OvBVt/3P93noIrf068x0xXkvxbn+fpigcqfNamiJ5CjGyfx9zAIs7zcHwbxjOw0Uih4SllfgtK+svNTeE0r5atMWE0xR489BvsqNuPSxYJUmW28JqhaSZ4SabYrRx114KcU6ko7hkjyPkjQa3P+chStJjIKYgu5tWBiMJp5QVLelKoM+xkY6S7efvJ8AfajxCViLGyDQPDviGr2D0VvIBob0D1ZmAoTvYOWafcNCaqaejPDybFtuLFX3pZBqfyOCyyzGhucyCmfBXJALKbhjRAqN5glNsUmGhhPK87TuGATQfVuZtenMvXMQIDAQAB", "manifest_version": 2, "name": "Brave", "permissions": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "webNavigation", "webRequest", "*://*/*", "chrome://favicon/*", "webDiscovery", "webRequestBlocking", "unlimitedStorage", "<all_urls>"], "version": "1.0.0"}, "path": "/opt/brave.com/brave/resources/brave_extension", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gcm": {"product_category_for_subtypes": "com.brave.linux"}, "google": {"services": {"signin_scoped_device_id": "f54b4873-8f06-4c2d-90f2-2462cb13421c"}}, "https_upgrade_navigations": {"2025-06-10": 20, "2025-06-17": 20, "2025-06-21": 10}, "in_product_help": {"new_badge": {"ComposeNudge": {"feature_enabled_time": "13392568061280862", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "13392568061280869", "show_count": 0, "used_count": 0}}, "policy_last_heavyweight_promo_time": "13394072134841396", "recent_session_enabled_time": "13392568061280575", "recent_session_start_times": ["13395020522557770", "13394639947403927", "13394071499523858", "13393724068516013", "13393154328766097", "13392987295121657", "13392568061280575"], "session_last_active_time": "13395020951502401", "session_start_time": "13395020522557770", "snoozed_feature": {"IPH_DiscardRing": {"first_show_time": "13394072134841400", "is_dismissed": true, "last_dismissed_by": 3, "last_show_time": "13394072134841400", "last_snooze_time": "0", "promo_index": 0, "show_count": 1, "shown_for_apps": [], "snooze_count": 0}}}, "intl": {"accept_languages": "fi,en-US,en", "selected_languages": "fi,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "language_model_counters": {"fi": 65}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"enable_media_router": true, "receiver_id_hash_token": "zubB5QImJKv8Hqhm7vT4gFdqmq/1povmCvY4fsmrEVEVPRlfPdbg/RLSEwxxhKzedeYf4/tn0278hrHh3yxIpw=="}, "ntp": {"num_personal_suggestions": 0}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "pinned_tabs": [], "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false, "m1": {"ad_measurement_enabled": false, "fledge_enabled": false, "topics_enabled": false}}, "profile": {"avatar_index": "60", "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.hameensanomat.fi:443,*": {"last_modified": "*********36031218", "setting": {"https://www.hameensanomat.fi/": {"couldShowBannerEvents": 1.3394665036031216e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "*********36031125"}}}}, "https://www.ksml.fi:443,*": {"last_modified": "13394072006278658", "setting": {"https://www.ksml.fi/": {"couldShowBannerEvents": 1.3394072006278656e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13394072006278548"}}}}, "https://www.mtvuutiset.fi:443,*": {"last_modified": "13394646671916318", "setting": {"https://www.mtvuutiset.fi/": {"couldShowBannerEvents": 1.3394072984811002e+16, "next_install_text_animation": {"delay": "172800000000", "last_shown": "13394646671916312"}}}}, "https://www.seiska.fi:443,*": {"last_modified": "13395020795740378", "setting": {"https://www.seiska.fi/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "13395020795740262"}}, "https://www.seiska.fi/?utm_source=pwa": {"couldShowBannerEvents": 1.3395020795740374e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "braveShields": {}, "braveShieldsMetadata": {"https://[*.]ampparit.com,*": {"last_modified": "13392568061374148", "setting": {"farbling_token": "58F6F01CAD21AB82B9800D6507C264D0"}}, "https://[*.]asuntolainankorko.fi,*": {"last_modified": "13394664919422504", "setting": {"farbling_token": "9B139C918A88B84D91A386CD9FFA072A"}}, "https://[*.]episodi.fi,*": {"last_modified": "13395020905763272", "setting": {"farbling_token": "79F1A303B8FC693FB4C6352868707C1C"}}, "https://[*.]google.com,*": {"last_modified": "13394664901464902", "setting": {"farbling_token": "839585939BF0EC44170EE0D8445B16B1"}}, "https://[*.]hameensanomat.fi,*": {"last_modified": "*********34917336", "setting": {"farbling_token": "36FC8E2C462C49FAA9BE4767AB187252"}}, "https://[*.]iltalehti.fi,*": {"last_modified": "13393023010251526", "setting": {"farbling_token": "6183219F3C1F09FAA3082E8C17091812"}}, "https://[*.]is.fi,*": {"last_modified": "13393724090043873", "setting": {"farbling_token": "9506840CABE249B43CC5D9F04E53F450"}}, "https://[*.]kauppalehti.fi,*": {"last_modified": "13394639992076479", "setting": {"farbling_token": "B5039A008CF65F831BB6709340E6C3FC"}}, "https://[*.]ksml.fi,*": {"last_modified": "13394072005410723", "setting": {"farbling_token": "1E4D505C02E7A613F824797BA86E54DF"}}, "https://[*.]mtvuutiset.fi,*": {"last_modified": "13394072984021371", "setting": {"farbling_token": "18137034688FD031E41AD551318D3D84"}}, "https://[*.]satakunnankansa.fi,*": {"last_modified": "13394664805181320", "setting": {"farbling_token": "9FD06FF881B5CD34AE013F73681F8BD9"}}, "https://[*.]seiska.fi,*": {"last_modified": "13395020794635037", "setting": {"farbling_token": "2D66931898F9761F869E3F24777993D6"}}, "https://[*.]stara.fi,*": {"last_modified": "13394665791465370", "setting": {"farbling_token": "8EDA87AF8D00013261C1D5BE6B8E5DB9"}}, "https://[*.]suomenmaa.fi,*": {"last_modified": "13394071590311740", "setting": {"farbling_token": "A91D718C85FA64DDDE5891F4865AEEE3"}}, "https://[*.]tekniikkatalous.fi,*": {"last_modified": "13395020717257293", "setting": {"farbling_token": "D1EA8022A8AF07C528A865AB2DEFC4BB"}}, "https://[*.]twitter.com,*": {"last_modified": "13394071644379482", "setting": {"farbling_token": "D0A8099355F1B7DBE69BF841CFE9E508"}}, "https://[*.]verkkouutiset.fi,*": {"last_modified": "13393023073748730", "setting": {"farbling_token": "89CB0030B889B82C2B9310CAE9E0342C"}}}, "braveSpeedreader": {}, "brave_ethereum": {}, "brave_google_sign_in": {}, "brave_localhost_access": {}, "brave_open_ai_chat": {}, "brave_remember_1p_storage": {}, "brave_solana": {}, "brave_webcompat_audio": {}, "brave_webcompat_canvas": {}, "brave_webcompat_device_memory": {}, "brave_webcompat_event_source_pool": {}, "brave_webcompat_font": {}, "brave_webcompat_hardware_concurrency": {}, "brave_webcompat_keyboard": {}, "brave_webcompat_language": {}, "brave_webcompat_media_devices": {}, "brave_webcompat_none": {}, "brave_webcompat_plugins": {}, "brave_webcompat_screen": {}, "brave_webcompat_speech_synthesis": {}, "brave_webcompat_usb_device_serial_number": {}, "brave_webcompat_user_agent": {}, "brave_webcompat_web_sockets_pool": {}, "brave_webcompat_webgl": {}, "brave_webcompat_webgl2": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.google.com:443,*": {"last_modified": "13394664901755373", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://www.is.fi:443,*": {"last_modified": "13395020929846387", "setting": {"client_hints": [11, 14]}}, "https://www.satakunnankansa.fi:443,*": {"last_modified": "13394664805408608", "setting": {"client_hints": [11, 14]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "cosmeticFiltering": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "fingerprintingV2": {}, "formfill_metadata": {}, "geolocation": {"https://www.ampparit.com:443,*": {"last_modified": "13394071514524258", "setting": 2}, "https://www.iltalehti.fi:443,*": {"last_modified": "13394072415492618", "setting": 2}}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "httpUpgradableResources": {}, "http_allowed": {}, "httpsUpgrades": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://asuntolainankorko.fi:443,*": {"expiration": "13402440984100837", "last_modified": "13394664984100840", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.ampparit.com:443,*": {"expiration": "13400354682305558", "last_modified": "13392578682305560", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 5}}, "https://www.episodi.fi:443,*": {"expiration": "13402796916288670", "last_modified": "13395020916288672", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "13402440919558646", "last_modified": "13394664919558648", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.hameensanomat.fi:443,*": {"expiration": "13402441076087928", "last_modified": "*********76087930", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.iltalehti.fi:443,*": {"expiration": "13402796784883653", "last_modified": "13395020784883656", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 10}}, "https://www.is.fi:443,*": {"expiration": "13402796945129077", "last_modified": "13395020945129080", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 8}}, "https://www.kauppalehti.fi:443,*": {"expiration": "13402416002241605", "last_modified": "13394640002241608", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.ksml.fi:443,*": {"expiration": "13401848043155618", "last_modified": "13394072043155621", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.mtvuutiset.fi:443,*": {"expiration": "13402422705242794", "last_modified": "13394646705242796", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.satakunnankansa.fi:443,*": {"expiration": "13402440850193097", "last_modified": "13394664850193099", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.seiska.fi:443,*": {"expiration": "13402796801568054", "last_modified": "13395020801568057", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.stara.fi:443,*": {"expiration": "13402796871770200", "last_modified": "13395020871770203", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.suomenmaa.fi:443,*": {"expiration": "13401847604468569", "last_modified": "13394071604468572", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.tekniikkatalous.fi:443,*": {"expiration": "13402796728405057", "last_modified": "13395020728405060", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.verkkouutiset.fi:443,*": {"expiration": "13402796692322277", "last_modified": "13395020692322279", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 1.3395020591971238e+16, "mediaPlaybacks": 1, "visits": 12}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"https://www.ampparit.com:443,*": {"last_modified": "13392578682305891", "setting": {"Geolocation": {"ignore_count": 4, "ignore_embargo_days": 1.339257868230589e+16}}}, "https://www.iltalehti.fi:443,*": {"last_modified": "13394071721899964", "setting": {"Geolocation": {"ignore_count": 2}}}, "https://www.kauppalehti.fi:443,*": {"last_modified": "13394640002241990", "setting": {"Geolocation": {"ignore_count": 1}}}, "https://www.tekniikkatalous.fi:443,*": {"last_modified": "13395020728405384", "setting": {"Geolocation": {"ignore_count": 1}}}}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "referrers": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "shieldsAds": {}, "shieldsCookiesV3": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13395055900774027", "setting": {"lastEngagementTime": 1.3395026681977694e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 8.191980665874476}}, "chrome://settings/,*": {"last_modified": "13395055900774037", "setting": {"lastEngagementTime": 1.3394846586619208e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://asuntolainankorko.fi:443,*": {"last_modified": "13395055900773919", "setting": {"lastEngagementTime": 1.3394997033835738e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.********00000003}}, "https://www.ampparit.com:443,*": {"last_modified": "13395055900773868", "setting": {"lastEngagementTime": 1.3395027088788878e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 11.699999999999996, "rawScore": 55.04265848972683}}, "https://www.google.com:443,*": {"last_modified": "13395055900773906", "setting": {"lastEngagementTime": 1.3394996973203442e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.6}}, "https://www.hameensanomat.fi:443,*": {"last_modified": "13395055900773928", "setting": {"lastEngagementTime": 1.3394997103992488e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://www.iltalehti.fi:443,*": {"last_modified": "13395055900773951", "setting": {"lastEngagementTime": 1.3394996799118484e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 17.97816097313655}}, "https://www.is.fi:443,*": {"last_modified": "13395055900773960", "setting": {"lastEngagementTime": 1.3395027100773826e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 13.879712854929442}}, "https://www.ksml.fi:443,*": {"last_modified": "13395055900773970", "setting": {"lastEngagementTime": 1.339494213664734e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://www.mtvuutiset.fi:443,*": {"last_modified": "13395055900773979", "setting": {"lastEngagementTime": 1.3394978749722388e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.6313031401472}}, "https://www.satakunnankansa.fi:443,*": {"last_modified": "13395055900773988", "setting": {"lastEngagementTime": 1.3394996883718364e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://www.stara.fi:443,*": {"last_modified": "13395055900773997", "setting": {"lastEngagementTime": 1.3395027022173002e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 4.0687913312256}}, "https://www.suomenmaa.fi:443,*": {"last_modified": "13395055900774005", "setting": {"lastEngagementTime": 1.3394941715990076e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://www.verkkouutiset.fi:443,*": {"last_modified": "13395055900774016", "setting": {"lastEngagementTime": 1.339502675782589e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.7600000000000002, "rawScore": 18.057460358737515}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"geolocation": [{"action": 3, "prompt_disposition": 1, "time": "13392569516628178"}, {"action": 3, "prompt_disposition": 1, "time": "13392569620798129"}, {"action": 3, "prompt_disposition": 1, "time": "13392569691696555"}, {"action": 3, "prompt_disposition": 1, "time": "13392578682305863"}, {"action": 3, "prompt_disposition": 1, "time": "13393023028907212"}, {"action": 1, "prompt_disposition": 1, "time": "13394071514525344"}, {"action": 3, "prompt_disposition": 1, "time": "13394071721899950"}, {"action": 1, "prompt_disposition": 1, "time": "13394072415493221"}, {"action": 3, "prompt_disposition": 1, "time": "13394640002241975"}, {"action": 3, "prompt_disposition": 1, "time": "13395020728405373"}]}, "pref_version": 1}, "created_by_version": "136.1.78.102", "creation_time": "13392568061238442", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13395027100773826", "last_time_obsolete_http_credentials_removed": 1748096266.774184, "last_time_password_store_metrics_reported": **********.556462, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "ampparit", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"brave": {"ad_block": {"developer_mode": "F59142B67502DC56C4D3763941A5F271E22391B134DC3833BE6C25DA10E7804C"}}, "browser": {"show_home_button": "904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"}, "default_search_provider_data": {"template_url_data": "575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"}, "enterprise_signin": {"policy_recovery_token": "7D3124ECAF7E96407EB65EAF5A43B02C7EE5F2D4A9FA38A9F371F9E1B74D6383"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "5DFE336F53ABC82C8A7F5B6570951697919D79C6DB509DDDA4CF7FF49083C5F2", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "1D43CD5524DE9C14DFFC9CCE8D39DC0B0252D0822D411DDE664DC06988E80541", "mnojpmjdmbbfmejpflffifhffcmidifd": "1DD697C9EF3264EE9B109A2383CB616B864F33EFB6005B3175F355BBA170C094"}, "ui": {"developer_mode": "55A29C051727FCAC1BDA847CCDE40838B2068CFA589E88819EA9FB17E4BD7320"}}, "google": {"services": {"account_id": "E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486", "last_signed_in_username": "82DB8D884695C643C31778B7B50DBB376848E2F81B5A1AECDA34FD448CECD10D", "last_username": "24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}}, "homepage": "B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E", "homepage_is_newtabpage": "3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119", "media": {"storage_id_salt": "E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"}, "pinned_tabs": "699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8", "prefs": {"preference_reset_time": "95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"}, "safebrowsing": {"incidents_sent": "569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"}, "search_provider_overrides": "1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD", "session": {"restore_on_startup": "F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E", "startup_urls": "8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13395279722", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"errored_reading": false, "tab_count": 9, "time": "13394071501556984", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 10, "time": "13394073107664336", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394109143493037", "type": 0}, {"crashed": true, "time": "13394639947391961", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394639949635856", "type": 5}, {"errored_reading": false, "tab_count": 11, "time": "13394639949675184", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394655484591437", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394656666746753", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13394662199120729", "type": 2, "window_count": 0}, {"crashed": false, "time": "13394664640743353", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394664640746984", "type": 5}, {"errored_reading": false, "tab_count": 1, "time": "13394664640815212", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394670078476631", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394700783115222", "type": 0}, {"crashed": true, "time": "13395020522555377", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395020526289908", "type": 5}, {"errored_reading": false, "tab_count": 2, "time": "13395020526299016", "type": 1, "window_count": 1}, {"crashed": true, "time": "*********97566424", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13395316216164239", "type": 2, "window_count": 0}], "session_data_status": 1}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"apps": false, "autofill": false, "bookmarks": true, "cached_passphrase_type": 4, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": true, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": true, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": true, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": true, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": true, "send_tab_to_self": true, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": true, "themes": false, "user_consent": true, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": "djExUnI2MXolwyroKKOnOaw6TdQbw91qK0x0G2b55IILu+DMS0gUzCmu8ln4FGs53055CXyYtrRXHkYz0ZR0gaxpRQ=="}, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": false, "feature_status_for_sync_to_signin": 3, "first_full_sync_completed": true, "gaia_id": "B70E8635E7869BDEB3C83878E0E12737FB58703303C08C76F617C2182B55509B", "has_setup_completed": true, "keep_everything_synced": false, "local_device_guids_with_timestamp": [{"cache_guid": "XjNo2WKZvKxWJp3otd7kdQ==", "timestamp": 155035}], "passwords": false, "passwords_per_account_pref_migration_done": true, "payments": false, "preferences": false, "reading_list": false, "saved_tab_groups": false, "tabs": false, "themes": false, "transport_data_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": {"sync.bag_of_chips": "", "sync.birthday": "1", "sync.cache_guid": "XjNo2WKZvKxWJp3otd7kdQ==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "********"}}, "typed_urls": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"fi": 12}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://www.mtvuutiset.fi/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults_with_url": {"https://www.google.com/search?q=euribor+12&oq=euri&gs_lcrp=EgZjaHJvbWUqCggCEAAYsQMYgAQyBggAEEUYOTIKCAEQABixAxiABDIKCAIQABixAxiABDIPCAMQABgKGLEDGIAEGIoFMgcIBBAAGIAEMgoIBRAAGLEDGIAEMgoIBhAAGLEDGIAEMgwIBxAAGAoYsQMYgAQyBwgIEAAYgAQyBggJEC4YQNIBCDQ4NzNqMGo0qAIAsAIB&sourceid=chrome&ie=UTF-8&sei=xcxRaKXEHvC1wPAPlY708Qc": ")]}'\n[\"\",[\"iltalehti\",\"omavero\",\"etuovi\",\"oikotie\",\"asuntolainalaskuri\",\"lainalaskuri\",\"euribor 12 kk\",\"pö<PERSON><PERSON><PERSON>hkö\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIwLgCEhQKElZpaW1laXNpbW3DpHQgaGF1dAomCJBOEiEKH0xpaXR0eXkgdmlpbWVhaWthaXNpaW4gaGFrdWloaW4\\u003d\",\"google:suggestdetail\":[{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000}],\"google:suggesteventid\":\"-4299409822376827755\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}}