{"accessibility": {"captions": {"headless_caption_enabled": false}}, "alternate_error_pages": {"backup": false}, "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "13395267984566484"}, "brave": {"accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "AltGr", "Alt"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "brave_ads": {"notification_ads": [], "should_allow_ads_subdivision_targeting": false, "state": {"has_migrated": {"client": {"v7": true}, "confirmations": {"v8": true}, "v2": true}}}, "brave_search": {"last-used-ntp-search-engine": "search.brave.com"}, "default_accelerators": {"33000": ["BrowserBack", "Alt+ArrowLeft", "AltGr+ArrowLeft"], "33001": ["BrowserForward", "Alt+ArrowRight", "AltGr+ArrowRight"], "33002": ["Control+KeyR", "F5", "BrowserRefresh"], "33003": ["BrowserHome", "Alt+Home"], "33007": ["Control+Shift+KeyR", "Control+F5", "Shift+F5", "Control+BrowserRefresh", "Shift+BrowserRefresh"], "34000": ["Control+KeyN"], "34001": ["Control+Shift+KeyN"], "34012": ["Control+Shift+KeyW", "Alt+F4"], "34014": ["AppNew", "Control+KeyT"], "34015": ["Control+KeyW", "Control+F4", "AppClose"], "34016": ["Control+Tab", "Control+PageDown"], "34017": ["Control+Shift+Tab", "Control+PageUp"], "34018": ["Control+Digit1", "Control+Numpad1", "Alt+Digit1", "Alt+Numpad1"], "34019": ["Control+Digit2", "Control+Numpad2", "Alt+Digit2", "Alt+Numpad2"], "34020": ["Control+Digit3", "Control+Numpad3", "Alt+Digit3", "Alt+Numpad3"], "34021": ["Control+Digit4", "Control+Numpad4", "Alt+Digit4", "Alt+Numpad4"], "34022": ["Control+Digit5", "Control+Numpad5", "Alt+Digit5", "Alt+Numpad5"], "34023": ["Control+Digit6", "Control+Numpad6", "Alt+Digit6", "Alt+Numpad6"], "34024": ["Control+Digit7", "Control+Numpad7", "Alt+Digit7", "Alt+Numpad7"], "34025": ["Control+Digit8", "Control+Numpad8", "Alt+Digit8", "Alt+Numpad8"], "34026": ["Control+Digit9", "Control+Numpad9", "Alt+Digit9", "Alt+Numpad9"], "34028": ["Control+Shift+KeyT"], "34030": ["F11"], "34032": ["Control+Shift+PageDown"], "34033": ["Control+Shift+PageUp"], "35000": ["Control+KeyD"], "35001": ["Control+Shift+KeyD"], "35002": ["Control+KeyU"], "35003": ["Control+KeyP"], "35004": ["Control+KeyS"], "35007": ["Control+Shift+KeyP"], "35031": ["Control+Shift+KeyS"], "37000": ["Control+KeyF"], "37001": ["Control+KeyG", "F3"], "37002": ["Control+Shift+KeyG", "Shift+F3"], "37003": ["Escape"], "38001": ["Control+Equal", "Control+NumpadAdd", "Control+Shift+Equal"], "38002": ["Control+Digit0", "Control+Numpad0"], "38003": ["Control+Minus", "Control+NumpadSubtract", "Control+Shift+Minus"], "39000": ["Alt+Shift+KeyT"], "39001": ["Control+KeyL", "Alt+KeyD"], "39002": ["BrowserSearch", "Control+KeyE", "Control+KeyK"], "39003": ["F10", "Alt", "Alt", "AltGr"], "39004": ["F6"], "39005": ["Shift+F6"], "39006": ["Alt+Shift+KeyB"], "39007": ["Alt+Shift+KeyA"], "39009": ["Control+F6"], "40000": ["Control+KeyO"], "40004": ["Control+Shift+KeyI"], "40005": ["Control+Shift+KeyJ"], "40009": ["BrowserFavorites", "Control+Shift+KeyB"], "40010": ["Control+KeyH"], "40011": ["Control+Shift+KeyO"], "40012": ["Control+KeyJ"], "40013": ["Control+Shift+Delete"], "40019": ["F1"], "40021": ["Alt+KeyE", "Alt+KeyF"], "40023": ["Control+Shift+KeyC"], "40134": ["Control+Shift+KeyM"], "40237": ["F12"], "40260": ["F7"], "40286": ["Shift+Escape"], "52500": ["Control+Shift+KeyA"], "56003": ["Alt+Shift+KeyN"], "56041": ["Control+KeyM"], "56044": ["Control+KeyB"], "56301": ["Control+Space"]}, "default_private_search_provider_data": {"alternate_urls": [], "contextual_search_url": "", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://cdn.search.brave.com/serp/favicon.ico", "featured_by_policy": false, "id": "0", "image_search_branding_label": "", "image_translate_source_language_param_key": "", "image_translate_target_language_param_key": "", "image_translate_url": "", "image_url": "", "image_url_post_params": "", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": ":br", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": false, "prefetch_likely_navigations": false, "prepopulate_id": 550, "safe_for_autoreplace": true, "search_intent_params": [], "search_url_post_params": "", "short_name": "Brave", "starter_pack_id": 0, "suggestions_url": "https://search.brave.com/api/suggest?q={searchTerms}&rich=true&source=desktop", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "https://search.brave.com/search?q={searchTerms}&source=desktop", "usage_count": 0}, "default_private_search_provider_guid": "485bf7d3-0215-45af-87dc-************", "enable_media_router_on_restart": true, "enable_window_closing_confirm": true, "migrated_search_default_in_jp": true, "rewards": {"notifications": "{\"displayed\":[],\"notifications\":[]}", "scheduled_captcha": {"failed_attempts": 0, "id": "", "paused": false, "payment_id": ""}}, "search": {"default_version": 32}, "shields_settings_version": 4, "stats": {"ads_blocked": "196", "bandwidth_saved_bytes": "7145482", "daily_saving_predictions_bytes": [{"day": **********.0, "value": 282635.0}, {"day": **********.0, "value": 558849.0}, {"day": **********.0, "value": 3183537.0}, {"day": **********.0, "value": 552142.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "today": {"p3a_total_card_views": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_card_visits": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}], "p3a_total_sidebar_filter_usages": [{"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}, "wallet": {"aurora_mainnet_migrated": true, "custom_networks": {"goerli_migrated": true}, "eip1559_chains_migrated": true, "is_compressed_nft_migrated": true, "is_spl_token_program_migrated": true, "keyrings": {}, "last_transaction_sent_time_dict": {}}, "webtorrent_enabled": false, "weekly_storage": {"Brave": {"P2A": {"new_tab_page_ad": {"opportunities": [{"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 2.0}, {"day": **********.0, "value": 4.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}}}, "search_count": [{"day": **********.0, "value": 3.0}, {"day": **********.0, "value": 1.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}, {"day": **********.0, "value": 0.0}]}}, "brave_shields": {"p3a_ads_allow_domain_count": 0, "p3a_ads_standard_domain_count": 0, "p3a_ads_strict_domain_count": 0, "p3a_first_reported_revision": 3, "p3a_fp_allow_domain_count": 0, "p3a_fp_standard_domain_count": 0, "p3a_fp_strict_domain_count": 0}, "brave_sync_v2": {"account_deleted_notice_pending": false, "reset_devices_progress_token_time": "*****************", "seed": "djExnE88VoEjemn66hYkijwcvlJUyml3ToBvkIQKujBK+fp9I1DI4QiZsILkVwza1UhQoHw1I20usWNaeI1Fys994LJWKvXQEsDVU/H4NxNDH3+mostDEaRfG+BB0EF2d1YeasZhAZLzK2YxS8nME9TB9k2Yh7fp88ScFOp3o7wvlGcG1QfX5IkzHCdoP+svatgb"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1080, "left": 1920, "maximized": true, "right": 3840, "top": 0, "work_area_bottom": 1080, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17993, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "78", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "87af97ae-14d3-47f8-a3f7-46538af7e037", "ephemeral_storage": {"first_party_storage_origins_to_cleanup": []}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "************", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Brave.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Brave Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/opt/brave.com/brave/resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://brave-resources/*", "chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://brave-resources chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://brave-resources/", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/opt/brave.com/brave/resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnojpmjdmbbfmejpflffifhffcmidifd": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "unlimitedStorage", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["*://*/*", "<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_scripts": [{"all_frames": true, "js": ["out/webstore.bundle.js"], "matches": ["https://chrome.google.com/webstore/*", "https://chromewebstore.google.com/*"], "run_at": "document_start"}], "content_security_policy": "default-src 'self'; font-src 'self' data:; script-src 'self' 'wasm-eval'; style-src 'unsafe-inline'; img-src 'self' data: chrome://favicon/; connect-src * data: blob: filesystem:;", "default_locale": "en_US", "description": "__MSG_appName__", "icons": {"128": "assets/img/icon-128.png", "16": "assets/img/icon-16.png", "256": "assets/img/icon-256.png", "32": "assets/img/icon-32.png", "48": "assets/img/icon-48.png", "64": "assets/img/icon-64.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAupOLMy5Fd4dCSOtjcApsAQOnuBdTs+OvBVt/3P93noIrf068x0xXkvxbn+fpigcqfNamiJ5CjGyfx9zAIs7zcHwbxjOw0Uih4SllfgtK+svNTeE0r5atMWE0xR489BvsqNuPSxYJUmW28JqhaSZ4SabYrRx114KcU6ko7hkjyPkjQa3P+chStJjIKYgu5tWBiMJp5QVLelKoM+xkY6S7efvJ8AfajxCViLGyDQPDviGr2D0VvIBob0D1ZmAoTvYOWafcNCaqaejPDybFtuLFX3pZBqfyOCyyzGhucyCmfBXJALKbhjRAqN5glNsUmGhhPK87TuGATQfVuZtenMvXMQIDAQAB", "manifest_version": 2, "name": "Brave", "permissions": ["activeTab", "alarms", "contentSettings", "contextMenus", "cookies", "history", "management", "settingsPrivate", "storage", "tabs", "webNavigation", "webRequest", "*://*/*", "chrome://favicon/*", "webDiscovery", "webRequestBlocking", "unlimitedStorage", "<all_urls>"], "version": "1.0.0"}, "path": "/opt/brave.com/brave/resources/brave_extension", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gcm": {"product_category_for_subtypes": "com.brave.linux", "push_messaging_unsubscribed_entries_list": []}, "google": {"services": {"signin_scoped_device_id": "bbf248b2-6aae-46e2-9a62-74898cc7ccfd"}}, "https_upgrade_navigations": {"2025-06-23": 18, "2025-06-24": 2}, "in_product_help": {"new_badge": {"ComposeNudge": {"feature_enabled_time": "13392568074244520", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "13392568074244526", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13392568074244321", "recent_session_start_times": ["13395308330407470", "13395181584514115", "13395011151193288", "13394927413462162", "13394665837188075", "13394584961459442", "13394148714301822", "13393331455835582", "13393154328766161", "13393019785650244"], "session_last_active_time": "13395315916785649", "session_start_time": "13395308330407470"}, "intl": {"accept_languages": "fi,en-US,en", "selected_languages": "fi,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "language_model_counters": {"en": 32, "fi": 49, "so": 1}, "media": {"device_id_salt": "26CF39AC48277D3BBBDC572BC646ECB6", "engagement": {"schema_version": 5}}, "media_router": {"enable_media_router": true, "receiver_id_hash_token": "u+96IlVo7PQGW3FThddQVajoLBcLU5TW1soUEKA+o65WJCtfEb/5F4jxAtZlWr3R8t4Clxu+g3CvN1S8Mx1iSA=="}, "ntp": {"num_personal_suggestions": 1}, "omnibox": {"shown_count_history_scope_promo": 2}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "permission_lifetime": {"expirations": {}}, "pinned_tabs": [], "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false, "m1": {"ad_measurement_enabled": false, "fledge_enabled": false, "topics_enabled": false}}, "profile": {"avatar_index": "61", "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {"https://[*.]youtube.com,https://[*.]google.com": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": 1}}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://ai.google.dev:443,*": {"last_modified": "13395183715391289", "setting": {"https://ai.google.dev/": {"couldShowBannerEvents": 1.3395183715391286e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13395183715391195"}}}}, "https://firebase.google.com:443,*": {"last_modified": "13395184911901511", "setting": {"https://firebase.google.com/": {"couldShowBannerEvents": 1.3395184911901508e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13395184911901372"}}}}, "https://github.com:443,*": {"last_modified": "13395182789589191", "setting": {"https://github.com/": {"couldShowBannerEvents": 1.3395182789589188e+16, "next_install_text_animation": {"delay": "86400000000", "last_shown": "13395182789589062"}}}}, "https://www.facebook.com:443,*": {"last_modified": "13394148720007147", "setting": {"https://www.facebook.com/": {"next_install_text_animation": {"delay": "1382400000000", "last_shown": "13394148720007139"}}, "https://www.facebook.com/?ref=homescreenpwa": {"couldShowBannerEvents": 1.3392568075631964e+16}}}, "https://www.youtube.com:443,*": {"last_modified": "13395183962768138", "setting": {"https://www.youtube.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "13395183962768017"}}, "https://www.youtube.com/?feature=ytca": {"couldShowBannerEvents": 1.3395183962768136e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "braveShields": {}, "braveShieldsMetadata": {"https://[*.]facebook.com,*": {"last_modified": "13395188790817469", "setting": {"farbling_token": "2F707EB5B259ED65C734CC6DE7F7B086"}}, "https://[*.]google.com,*": {"last_modified": "13395189383421482", "setting": {"farbling_token": "6E1D02B13B9FBC0EAA57D1EBE8AF15B6"}}, "https://[*.]techcrunch.com,*": {"last_modified": "13395189976125509", "setting": {"farbling_token": "0C44E8742F99CFEE2CDD6E511D05C2AE"}}, "https://[*.]youtube.com,*": {"last_modified": "13395189511096273", "setting": {"farbling_token": "3302AB6B72301B2ED7092B024ECB7775"}}}, "braveSpeedreader": {}, "brave_ethereum": {}, "brave_google_sign_in": {}, "brave_localhost_access": {}, "brave_open_ai_chat": {}, "brave_remember_1p_storage": {}, "brave_solana": {}, "brave_webcompat_audio": {}, "brave_webcompat_canvas": {}, "brave_webcompat_device_memory": {}, "brave_webcompat_event_source_pool": {}, "brave_webcompat_font": {}, "brave_webcompat_hardware_concurrency": {}, "brave_webcompat_keyboard": {}, "brave_webcompat_language": {}, "brave_webcompat_media_devices": {}, "brave_webcompat_none": {}, "brave_webcompat_plugins": {}, "brave_webcompat_screen": {}, "brave_webcompat_speech_synthesis": {}, "brave_webcompat_usb_device_serial_number": {}, "brave_webcompat_user_agent": {}, "brave_webcompat_web_sockets_pool": {}, "brave_webcompat_webgl": {}, "brave_webcompat_webgl2": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://developers.facebook.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [11, 14, 23]}}, "https://jules.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://studio.firebase.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://www.facebook.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [11, 14, 23]}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}, "https://www.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 14, 16, 23, 25]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "cosmeticFiltering": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "fingerprintingV2": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "httpUpgradableResources": {}, "http_allowed": {}, "httpsUpgrades": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://business.whatsapp.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://developers.facebook.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 4}}, "https://developers.googleblog.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://firebase.google.com:443,*": {"expiration": "13402961292366984", "last_modified": "13395185292366987", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://firebase.studio:443,*": {"expiration": "13402961363145509", "last_modified": "13395185363145512", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://github.com:443,*": {"expiration": "13402959714322271", "last_modified": "13395183714322273", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://jules.google:443,*": {"expiration": "13402789436825610", "last_modified": "13395013436825613", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://private-user-images.githubusercontent.com:443,*": {"expiration": "13402958889561551", "last_modified": "13395182889561554", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.facebook.com:443,*": {"expiration": "13402796519587774", "last_modified": "13395020519587777", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 1.3395011430563392e+16, "mediaPlaybacks": 2, "visits": 10}}, "https://www.google.com:443,*": {"expiration": "13402960910609439", "last_modified": "13395184910609440", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 7}}, "https://www.youtube.com:443,*": {"expiration": "13402960705005266", "last_modified": "13395184705005269", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 1.339518401206988e+16, "mediaPlaybacks": 1, "visits": 2}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "referrers": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "shieldsAds": {}, "shieldsCookiesV3": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395285684323434e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 23.***************}}, "chrome://settings/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.339499261729235e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395286168587626e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.***************}}, "https://ai.google.dev:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395284697844222e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://business.whatsapp.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395076436640858e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://developers.facebook.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395076682406938e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 22.**************}}, "https://developers.googleblog.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395283470363294e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7600000000000002}}, "https://firebase.google.com:443,*": {"last_modified": "13395314968587767", "setting": {"lastEngagementTime": 1.3395285719613422e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://firebase.studio:443,*": {"last_modified": "13395314968587939", "setting": {"lastEngagementTime": 1.3395286157011254e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://github.com:443,*": {"last_modified": "13395314968587732", "setting": {"lastEngagementTime": 1.339528450660031e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "https://jules.google:443,*": {"last_modified": "13395314968587877", "setting": {"lastEngagementTime": 1.3395246496428602e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.7}}, "https://techcrunch.com:443,*": {"last_modified": "13395314968587814", "setting": {"lastEngagementTime": 1.3395283580028246e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 2.1}}, "https://www.facebook.com:443,*": {"last_modified": "13395314968587713", "setting": {"lastEngagementTime": 1.339528568420199e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 100.0}}, "https://www.google.com:443,*": {"last_modified": "13395314968587781", "setting": {"lastEngagementTime": 1.339528569647989e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 16.014765843920557}}, "https://www.youtube.com:443,*": {"last_modified": "13395314968587842", "setting": {"lastEngagementTime": 1.3395286073281282e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 12.599999999999987}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"notifications": [{"action": 1, "prompt_disposition": 1, "time": "13393019841614803"}, {"action": 1, "prompt_disposition": 1, "time": "13393331472710776"}]}, "pref_version": 1}, "created_by_version": "136.1.78.102", "creation_time": "13392568074204538", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13395286168587626", "last_time_obsolete_http_credentials_removed": 1748096267.448996, "last_time_password_store_metrics_reported": **********.758488, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "facebook", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"brave": {"ad_block": {"developer_mode": "F59142B67502DC56C4D3763941A5F271E22391B134DC3833BE6C25DA10E7804C"}}, "browser": {"show_home_button": "904452986128BBEE5A7B1FFB8F342100C3150E3D9FD76C4105DF33EB021E22FD"}, "default_search_provider_data": {"template_url_data": "575D258E47F940C6887685ABA99A5839CBFE4BA30863349DFE0D0C375AAB8816"}, "enterprise_signin": {"policy_recovery_token": "7D3124ECAF7E96407EB65EAF5A43B02C7EE5F2D4A9FA38A9F371F9E1B74D6383"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "E5317512D3593F17180141DA1AF7B2CA31FB816B85976E3D5B73531BCDF9592A", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "8E7147D283E3D6D639B990FED39F1E797DB7C943FA42C1505AFBAB6BD299CCD8", "mnojpmjdmbbfmejpflffifhffcmidifd": "E85F15924850C142BE48EEE197678E64FA2F1593ED3618F7E9630D0678E0C10B"}, "ui": {"developer_mode": "55A29C051727FCAC1BDA847CCDE40838B2068CFA589E88819EA9FB17E4BD7320"}}, "google": {"services": {"account_id": "E5B4CD7C5FA271A47D07D462465AFD63DBF6A8CDFAFEF4839D13F8F552131486", "last_signed_in_username": "82DB8D884695C643C31778B7B50DBB376848E2F81B5A1AECDA34FD448CECD10D", "last_username": "24FCEF9BF7DF12A2935BE143E58951E09DBAA1D3E0E24430C0FF93009F5D6AFD"}}, "homepage": "B1E9FE8108A84F532486D13AAC43C0AFDA16D3DFC9EB2F743AEE11F89F2F163E", "homepage_is_newtabpage": "3680F776D17E3C099431BAF5381FAB9BCC0C2C70FEA4C74D12324BC94A207119", "media": {"storage_id_salt": "E1848263E6199A89D48A7FDF168364BF0F31246A18227F3D149D4088C7F4D667"}, "pinned_tabs": "699F1AC92729A024B80605AFC3C63BFB2A35B70C4214581BBE108F851528E9E8", "prefs": {"preference_reset_time": "95C909F3D0669D5931907B455F099C510E7770D9F0BA6FF13E4C76101B44F757"}, "safebrowsing": {"incidents_sent": "569707D9A4676B72F48BE92B740BE3EF895419C8A646F1AE1BA70BD9C3B41845"}, "search_provider_overrides": "1E1EBA3A4DC28A23BEFCF6ED5D71CE71E9814DD587A305F6B14F72E834AF75DD", "session": {"restore_on_startup": "F9BD26F5D1AA6AB5258754888529CB2A82AE68D1703BCC2A97DEAEE5DDDA190E", "startup_urls": "8BB8DBC1D7CA5C58F821C38254FB2B9C874F8EE9B9905B57DE48C731C6C91837"}}}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13395267984", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "savefile": {"default_directory": "/home/<USER>/Downloads"}, "sessions": {"event_log": [{"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13394655484591300", "type": 2, "window_count": 0}, {"crashed": true, "time": "13394665837176684", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394665846906017", "type": 5}, {"errored_reading": false, "tab_count": 2, "time": "13394665846914335", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394670078476707", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394700783151727", "type": 0}, {"crashed": true, "time": "13394927413459242", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13394927415580792", "type": 5}, {"errored_reading": false, "tab_count": 2, "time": "13394927415590552", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394932808148151", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395011151174398", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395011151176514", "type": 5}, {"errored_reading": false, "tab_count": 1, "time": "13395011151246676", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395020519584995", "type": 2, "window_count": 1}, {"crashed": true, "time": "13395181584511230", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13395181586683424", "type": 5}, {"errored_reading": false, "tab_count": 2, "time": "13395181586692304", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 5, "time": "13395316216164408", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395316874757536", "type": 0}], "session_data_status": 1}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"apps": false, "autofill": false, "bookmarks": true, "cached_passphrase_type": 4, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": true, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": true, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": true, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": true, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": true, "send_tab_to_self": true, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": true, "themes": false, "user_consent": true, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": "djExUnI2MXolwyroKKOnOaw6TdQbw91qK0x0G2b55IILu+DMS0gUzCmu8ln4FGs53055CXyYtrRXHkYz0ZR0gaxpRQ=="}, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": false, "feature_status_for_sync_to_signin": 3, "first_full_sync_completed": true, "gaia_id": "B70E8635E7869BDEB3C83878E0E12737FB58703303C08C76F617C2182B55509B", "has_setup_completed": true, "keep_everything_synced": false, "local_device_guids_with_timestamp": [{"cache_guid": "AzsRA0Ww94vyUmkbCsmaCg==", "timestamp": 155038}], "passwords": false, "passwords_per_account_pref_migration_done": true, "payments": false, "preferences": false, "reading_list": false, "saved_tab_groups": false, "tabs": false, "themes": false, "transport_data_per_account": {"+cOlNvs/x3/WSzIDqNgc0NbFS4jerEshZsRleO1cTT0=": {"sync.bag_of_chips": "", "sync.birthday": "1", "sync.cache_guid": "AzsRA0Ww94vyUmkbCsmaCg==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "********"}}, "typed_urls": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 1, "fi": 16}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://ai.google.dev/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}, "https://www.facebook.com/?ref=homescreenpwa": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}, "https://www.youtube.com/?feature=ytca": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 2, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"urho kekkosen kansallispuisto\",\"harri he<PERSON>\",\"vain elämää\",\"administer ostaa saras<PERSON>\",\"karhu parik<PERSON>\",\"eveliina salonen\",\"finnair i<PERSON> lak<PERSON>\",\"finnair doha\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChgIkk4SEwoPVHJlbmRhYXZhdCBoYXV0KAo\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"google:entityinfo\":\"CgovbS8wZ3R3YjUxEhlTdW9tYWxhaW5lbiB0ZW5uaXNwZWxhYWphMqsIZGF0YTppbWFnZS9qcGVnO2Jhc2U2NCwvOWovNEFBUVNrWkpSZ0FCQVFBQUFRQUJBQUQvMndDRUFBa0dCd2dIQmdrSUJ3Z0tDZ2tMRFJZUERRd01EUnNVRlJBV0lCMGlJaUFkSHg4a0tEUXNKQ1l4Sng4ZkxUMHRNVFUzT2pvNkl5cy9SRDg0UXpRNU9qY0JDZ29LRFF3TkdnOFBHamNsSHlVM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOLy9BQUJFSUFDc0FRQU1CSWdBQ0VRRURFUUgveEFBYUFBQUNBd0VCQUFBQUFBQUFBQUFBQUFBREJBRUNCUUFHLzhRQU1CQUFBUU1DQkFNRkNBTUFBQUFBQUFBQUFRQUNBd1FSQlJJaE1VRlJZUVlUazlIU0ZGSlRjWkdTb2JFaU1rTC94QUFhQVFBQ0FnTUFBQUFBQUFBQUFBQUFBQUFFQlFNR0FBRUMvOFFBSUJFQUFnSUNBZ0lEQUFBQUFBQUFBQUFBQVFJQUVRTlJJVUVpc1FRU1lmL2FBQXdEQVFBQ0VRTVJBRDhBZHlxREdqWlZCYXJKY3JsUlowWTVJWmlISk5scW9XcnE1d1ZFMWNEb2NIa2djK3ZwYW1WMjF4STBNSFVXSUs4OWlOUFRpcmxGR2M5UG12RzRqVXQ0STFSVnlZZkMrb1l3UHlXSlk0WGFlR3YxNkpkdGY3WlZITmxMM2d1SkRiRFMzQlJZVXpqSXpzZkhxVFp6OFpzS0lvcHU0cTZuK1NFNkN5MFh0UVhzMFJRWXhlMk1UY3N1eWhXR215NitxR2g4b1dvWkNNVlE3cmMwUk1idFRUUGZncGt0YUx2QU0zUGZaSDdOVThOWDJla3FYTXRVMGNnaXpBQUJ6YkEzUEVuWGorRnBWK0M0aDJrcForNHMrVVpXaVNWOWczb09scm15bkNPenVKWVZoMVhUVkV0T3p2cG96bk1oTFEwYU9KMDBObEJrelZqQUxVMWpqOGhtTENDV29XdEhrNzNFWE5DQTlvVDlhS2NTQnRKbmN4b3NYdjhBOW5uYmdPaVRjTmRrVXBzWEZyaXVKc1pxWDNwdnRIbXV2Uys5TjlnODBvcENGczdoZkdvMFRTMi92TjRZODFTOUw4U2Z3eDZrQys2aFpaM01vYW5vY0ZxWWFha210VU9ZeDV2Wnd5M3R6SUpzUG9rRzFrVS90akhUVEhJWFhEU1NBU045OWRGblZrajJVMERXbXdlZjVDMjlyMlFLTnhiaWswWU5teVFBdkhNM3Qra3NkenprUFJqWEdvOGNZN1gzRHVGSjhXYndoNmtNaW1HMHMvZ2oxSVR0bFFteDBUWDdOdUppRjFQLzJRPT06EUhhcnJpIEhlbGnDtnZhYXJhSgcjMDY0YmEzUj5nc19zc3A9ZUp6ajR0TFAxVGRJTHlsUE1qVTBZUFFTekVnc0tzcFV5RWpOeVR5OHJTd3hzU2dSQUo3ZUN4SXAGcAc\\u003d\",\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002}],\"google:suggesteventid\":\"-3078667981348595063\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"ENTITY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\"]}]", "cachedresults_with_url": {"https://www.google.com/search?q=firebase+studio&oq=fireb&gs_lcrp=EgZjaHJvbWUqBwgGEAAYgAQyBggAEEUYOTINCAEQLhjHARjRAxiABDIKCAIQLhixAxiABDINCAMQLhiDARixAxiABDIKCAQQLhjUAhiABDIKCAUQLhjUAhiABDIHCAYQABiABDIHCAcQABiABDIHCAgQABiABDIGCAkQLhhA0gEJMTAwNDFqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8": ")]}'\n[\"\",[\"cursor ai\",\"lovable\",\"google ai studio\",\"gemini api key\",\"firebase studio pricing\",\"notebooklm\",\"supabase\",\"windsurf\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIwLgCEhQKElZpaW1laXNpbW3DpHQgaGF1dAomCJBOEiEKH0xpaXR0eXkgdmlpbWVhaWthaXNpaW4gaGFrdWloaW4\\u003d\",\"google:suggestdetail\":[{\"zl\":10000},{\"google:entityinfo\":\"Cg0vZy8xMWxtbHYyX2xnEiVPaGplbG1pc3RveXJpdHlzIMK3IFN0b2NraG9sbSwgUnVvdHNpMl9odHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vcC9BRjFRaXBOb1ByVk83TFpEZ0FNQmxva1R0cXF3LTlpZ0Rpd1pZS2NTVlZSUT13OTItaDkyLW4tay1ubzoHTG92YWJsZVJnZ3Nfc3NwPWVKemo0dFZQMXpjMHpNbk5LVE9LejBrM1lMUlNNYWd3TVROTnMwd3hOak5Kc3pSSXM3Qk1zaktvU0RST01VNDJOMGt6TXpOSVRqYTA5R0xQeVM5TFRNcEpCUUF0X0JJZnAZ\",\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"zl\":10000},{\"google:entityinfo\":\"Cg0vZy8xMWs3ZGR4MmtiEg9PaGplbG1pc3Rva2V0anUywwdkYXRhOmltYWdlL2pwZWc7YmFzZTY0LC85ai80QUFRU2taSlJnQUJBUUFBQVFBQkFBRC8yd0NFQUFrR0J3Z0hCZ2tJQndnS0Nna0xEUllQRFF3TURSc1VGUkFXSUIwaUlpQWRIeDhrS0RRc0pDWXhKeDhmTFQwdE1UVTNPam82SXlzL1JEODRRelE1T2pjQkNnb0tEUXdOR2c4UEdqY2xIeVUzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM056YzNOemMzTnpjM04vL0FBQkVJQUF3QVFBTUJJZ0FDRVFFREVRSC94QUFaQUFFQUF3RUJBQUFBQUFBQUFBQUFBQUFHQWdNRUJ3SC94QUFyRUFBQ0FnRURBZ1FGQlFBQUFBQUFBQUFCQWdNRUVRQUZFaUVpRXhReFVRWUhKRUZ4SXpJellXTC94QUFXQVFFQkFRQUFBQUFBQUFBQUFBQUFBQUFBQkFIL3hBQWJFUUFDQVFVQUFBQUFBQUFBQUFBQUFBQUFBUU1DRVRHQjhQL2FBQXdEQVFBQ0VRTVJBRDhBZGJyOFJXWWR5Vzc1U1pxbFlrSkVRVTVaN2VST1A3MGoyRGRtM2lzMDVxTkFnT0ZKYlBMM3hxM2Zhc2QzYkpLMHhZSkk4WWJpZXY3MTFyZ2hqcndwRENnU05CeFZSOWhyRW1Sd3hTMFN1OWQxbmZJQld2ank3VmszZ1NSVXZvNVAwbFVsaDRRc0xFN3M2c1IwRFpJSVFxUmpCQUoxSzE4ZVN0WWFPalB0S3dOdkQ3ZEhjbmxKaEFGY1NjaVFlcDVFcmpJejBIVFRvVjRBMGpDR1BsTDBrUEFkL3dDZmZXUzFzdTMycGFjazFaUHBKR2tpVURDOGloUTVIb2UxaU91dExBTmMrWTFxSGJhdHhZcUdSRVpaNCtUSHhWRTVpRFJzU29DdHhMS2U0bkk3ZnZxemJQaW01VDMrM1RhelRzd3liNUpWOHEwek5hUlNNaDFHZWlMajB4NlpPUjZhZnZWcnljT2NFVGNCaGNvRHhIc1BiWG9yd0xMNHF3eGlUcjNoUm5yNjlkQWM3cC9NSGNiTlc0MGNPM3l5Q0N0WXJ1ajhVNHpTbE1ZZHdXSUF5QVRHVzlNTDAxT3g4d3J3ZW41YXRVa1Jxc1U3bDJFWG1DOGpSbFkrYnJ4d1YvMzFZRDJKZmluVkNPZ3JROFg2TXZoakRmblVqV2dQaFpoalBoZng1UWRuNDl0QWYvL1o6CFN1cGFiYXNlSgcjNDI0MjQyUjtnc19zc3A9ZUp6ajR0VlAxemMwekRaUFNha3d5azVTWURSZ2RHRHc0aWd1TFVoTVNpeE9CUUIzT3doWXAXigETaHR0cDovL3N1cGFiYXNlLmlvLw\\u003d\\u003d\",\"zl\":10000},{\"zl\":10000}],\"google:suggesteventid\":\"-831889025646831473\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[512,650,67,524,362,308],[512,650,67,199,175,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,524,362,308],[512,650,67,199,465,524,362,308],[512,650,67,524,362,308]],\"google:suggesttype\":[\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}}