from dataclasses import dataclass, asdict
from typing import Optional, List, Any, Dict, Union
from uuid import UUID


@dataclass
class AlternateErrorPages:
    backup: Optional[bool] = None


@dataclass
class Apps:
    shortcuts_arch: Optional[str] = None
    shortcuts_version: Optional[int] = None


@dataclass
class Autocomplete:
    retention_policy_last_version: Optional[int] = None


@dataclass
class Autofill:
    last_version_deduped: Optional[int] = None


@dataclass
class Client:
    v7: Optional[bool] = None


@dataclass
class Confirmations:
    v8: Optional[bool] = None


@dataclass
class HasMigrated:
    client: Optional[Client] = None
    confirmations: Optional[Confirmations] = None
    v2: Optional[bool] = None


@dataclass
class State:
    has_migrated: Optional[HasMigrated] = None


@dataclass
class BraveAds:
    notification_ads: Optional[List[Any]] = None
    should_allow_ads_subdivision_targeting: Optional[bool] = None
    state: Optional[State] = None


@dataclass
class DefaultPrivateSearchProviderData:
    alternate_urls: Optional[List[Any]] = None
    contextual_search_url: Optional[str] = None
    created_from_play_api: Optional[bool] = None
    date_created: Optional[int] = None
    doodle_url: Optional[str] = None
    enforced_by_policy: Optional[bool] = None
    favicon_url: Optional[str] = None
    featured_by_policy: Optional[bool] = None
    id: Optional[int] = None
    image_search_branding_label: Optional[str] = None
    image_translate_source_language_param_key: Optional[str] = None
    image_translate_target_language_param_key: Optional[str] = None
    image_translate_url: Optional[str] = None
    image_url: Optional[str] = None
    image_url_post_params: Optional[str] = None
    input_encodings: Optional[List[str]] = None
    is_active: Optional[int] = None
    keyword: Optional[str] = None
    last_modified: Optional[int] = None
    last_visited: Optional[int] = None
    logo_url: Optional[str] = None
    new_tab_url: Optional[str] = None
    originating_url: Optional[str] = None
    policy_origin: Optional[int] = None
    preconnect_to_search_url: Optional[bool] = None
    prefetch_likely_navigations: Optional[bool] = None
    prepopulate_id: Optional[int] = None
    safe_for_autoreplace: Optional[bool] = None
    search_intent_params: Optional[List[Any]] = None
    search_url_post_params: Optional[str] = None
    short_name: Optional[str] = None
    starter_pack_id: Optional[int] = None
    suggestions_url: Optional[str] = None
    suggestions_url_post_params: Optional[str] = None
    synced_guid: Optional[UUID] = None
    url: Optional[str] = None
    usage_count: Optional[int] = None


@dataclass
class ScheduledCAPTCHA:
    failed_attempts: Optional[int] = None
    id: Optional[str] = None
    paused: Optional[bool] = None
    payment_id: Optional[str] = None


@dataclass
class Rewards:
    notifications: Optional[str] = None
    scheduled_captcha: Optional[ScheduledCAPTCHA] = None


@dataclass
class Search:
    default_version: Optional[int] = None


@dataclass
class P3ATotal:
    day: Optional[int] = None
    value: Optional[int] = None


@dataclass
class Today:
    p3_a_total_card_views: Optional[List[P3ATotal]] = None
    p3_a_total_card_visits: Optional[List[P3ATotal]] = None
    p3_a_total_sidebar_filter_usages: Optional[List[P3ATotal]] = None


@dataclass
class CustomNetworks:
    goerli_migrated: Optional[bool] = None


@dataclass
class TranslateSiteBlocklistWithTime:
    pass


@dataclass
class Wallet:
    aurora_mainnet_migrated: Optional[bool] = None
    custom_networks: Optional[CustomNetworks] = None
    eip1559_chains_migrated: Optional[bool] = None
    is_compressed_nft_migrated: Optional[bool] = None
    is_spl_token_program_migrated: Optional[bool] = None
    keyrings: Optional[TranslateSiteBlocklistWithTime] = None
    last_transaction_sent_time_dict: Optional[TranslateSiteBlocklistWithTime] = None


@dataclass
class WelcomeBrave:
    accelerators: Optional[Dict[str, List[str]]] = None
    brave_ads: Optional[BraveAds] = None
    default_accelerators: Optional[Dict[str, List[str]]] = None
    default_private_search_provider_data: Optional[DefaultPrivateSearchProviderData] = None
    default_private_search_provider_guid: Optional[UUID] = None
    enable_media_router_on_restart: Optional[bool] = None
    migrated_search_default_in_jp: Optional[bool] = None
    rewards: Optional[Rewards] = None
    search: Optional[Search] = None
    shields_settings_version: Optional[int] = None
    today: Optional[Today] = None
    wallet: Optional[Wallet] = None
    webtorrent_enabled: Optional[bool] = None


@dataclass
class BraveShields:
    p3_a_ads_allow_domain_count: Optional[int] = None
    p3_a_ads_standard_domain_count: Optional[int] = None
    p3_a_ads_strict_domain_count: Optional[int] = None
    p3_a_first_reported_revision: Optional[int] = None
    p3_a_fp_allow_domain_count: Optional[int] = None
    p3_a_fp_standard_domain_count: Optional[int] = None
    p3_a_fp_strict_domain_count: Optional[int] = None


@dataclass
class BraveSyncV2:
    reset_devices_progress_token_time: Optional[str] = None


@dataclass
class WindowPlacement:
    bottom: Optional[int] = None
    left: Optional[int] = None
    maximized: Optional[bool] = None
    right: Optional[int] = None
    top: Optional[int] = None
    work_area_bottom: Optional[int] = None
    work_area_left: Optional[int] = None
    work_area_right: Optional[int] = None
    work_area_top: Optional[int] = None


@dataclass
class WelcomeBrowser:
    has_seen_welcome_page: Optional[bool] = None
    window_placement: Optional[WindowPlacement] = None


@dataclass
class DefaultSearchProvider:
    guid: Optional[str] = None


@dataclass
class DomainDiversity:
    last_reporting_timestamp: Optional[str] = None


@dataclass
class DownloadBubble:
    partial_view_impressions: Optional[int] = None


@dataclass
class EphemeralStorage:
    first_party_storage_origins_to_cleanup: Optional[List[Any]] = None


@dataclass
class Alerts:
    initialized: Optional[bool] = None


@dataclass
class ActivePermissions:
    api: Optional[List[str]] = None
    explicit_host: Optional[List[str]] = None
    manifest_permissions: Optional[List[Any]] = None
    scriptable_host: Optional[List[str]] = None


@dataclass
class Launch:
    web_url: Optional[str] = None


@dataclass
class App:
    launch: Optional[Launch] = None
    urls: Optional[List[str]] = None


@dataclass
class AhfgeienlihckogmohjhadlkjgocplebManifest:
    app: Optional[App] = None
    description: Optional[str] = None
    icons: Optional[Dict[str, str]] = None
    key: Optional[str] = None
    name: Optional[str] = None
    permissions: Optional[List[str]] = None
    version: Optional[str] = None


@dataclass
class Ahfgeienlihckogmohjhadlkjgocpleb:
    account_extension_type: Optional[int] = None
    active_permissions: Optional[ActivePermissions] = None
    app_launcher_ordinal: Optional[str] = None
    commands: Optional[TranslateSiteBlocklistWithTime] = None
    content_settings: Optional[List[Any]] = None
    creation_flags: Optional[int] = None
    disable_reasons: Optional[List[Any]] = None
    first_install_time: Optional[str] = None
    from_webstore: Optional[bool] = None
    incognito_content_settings: Optional[List[Any]] = None
    incognito_preferences: Optional[TranslateSiteBlocklistWithTime] = None
    last_update_time: Optional[str] = None
    location: Optional[int] = None
    manifest: Optional[AhfgeienlihckogmohjhadlkjgocplebManifest] = None
    needs_sync: Optional[bool] = None
    page_ordinal: Optional[str] = None
    path: Optional[str] = None
    preferences: Optional[TranslateSiteBlocklistWithTime] = None
    regular_only_preferences: Optional[TranslateSiteBlocklistWithTime] = None
    was_installed_by_default: Optional[bool] = None
    was_installed_by_oem: Optional[bool] = None


@dataclass
class PermissionClass:
    file_system: Optional[List[str]] = None


@dataclass
class MhjfbmdgcfjbbpaeojofohoefgiehjaiManifest:
    content_security_policy: Optional[str] = None
    description: Optional[str] = None
    incognito: Optional[str] = None
    key: Optional[str] = None
    manifest_version: Optional[int] = None
    mime_types: Optional[List[str]] = None
    mime_types_handler: Optional[str] = None
    name: Optional[str] = None
    offline_enabled: Optional[bool] = None
    permissions: Optional[List[Union[PermissionClass, str]]] = None
    version: Optional[int] = None


@dataclass
class Mhjfbmdgcfjbbpaeojofohoefgiehjai:
    account_extension_type: Optional[int] = None
    active_permissions: Optional[ActivePermissions] = None
    commands: Optional[TranslateSiteBlocklistWithTime] = None
    content_settings: Optional[List[Any]] = None
    creation_flags: Optional[int] = None
    disable_reasons: Optional[List[Any]] = None
    first_install_time: Optional[str] = None
    from_webstore: Optional[bool] = None
    incognito_content_settings: Optional[List[Any]] = None
    incognito_preferences: Optional[TranslateSiteBlocklistWithTime] = None
    last_update_time: Optional[str] = None
    location: Optional[int] = None
    manifest: Optional[MhjfbmdgcfjbbpaeojofohoefgiehjaiManifest] = None
    path: Optional[str] = None
    preferences: Optional[TranslateSiteBlocklistWithTime] = None
    regular_only_preferences: Optional[TranslateSiteBlocklistWithTime] = None
    was_installed_by_default: Optional[bool] = None
    was_installed_by_oem: Optional[bool] = None


@dataclass
class ContentScript:
    all_frames: Optional[bool] = None
    js: Optional[List[str]] = None
    matches: Optional[List[str]] = None
    run_at: Optional[str] = None


@dataclass
class MnojpmjdmbbfmejpflffifhffcmidifdManifest:
    content_scripts: Optional[List[ContentScript]] = None
    content_security_policy: Optional[str] = None
    default_locale: Optional[str] = None
    description: Optional[str] = None
    icons: Optional[Dict[str, str]] = None
    incognito: Optional[str] = None
    key: Optional[str] = None
    manifest_version: Optional[int] = None
    name: Optional[str] = None
    permissions: Optional[List[str]] = None
    version: Optional[str] = None


@dataclass
class Mnojpmjdmbbfmejpflffifhffcmidifd:
    account_extension_type: Optional[int] = None
    active_permissions: Optional[ActivePermissions] = None
    commands: Optional[TranslateSiteBlocklistWithTime] = None
    content_settings: Optional[List[Any]] = None
    creation_flags: Optional[int] = None
    disable_reasons: Optional[List[Any]] = None
    first_install_time: Optional[str] = None
    from_webstore: Optional[bool] = None
    incognito_content_settings: Optional[List[Any]] = None
    incognito_preferences: Optional[TranslateSiteBlocklistWithTime] = None
    last_update_time: Optional[str] = None
    location: Optional[int] = None
    manifest: Optional[MnojpmjdmbbfmejpflffifhffcmidifdManifest] = None
    path: Optional[str] = None
    preferences: Optional[TranslateSiteBlocklistWithTime] = None
    regular_only_preferences: Optional[TranslateSiteBlocklistWithTime] = None
    was_installed_by_default: Optional[bool] = None
    was_installed_by_oem: Optional[bool] = None


@dataclass
class PurpleSettings:
    ahfgeienlihckogmohjhadlkjgocpleb: Optional[Ahfgeienlihckogmohjhadlkjgocpleb] = None
    mhjfbmdgcfjbbpaeojofohoefgiehjai: Optional[Mhjfbmdgcfjbbpaeojofohoefgiehjai] = None
    mnojpmjdmbbfmejpflffifhffcmidifd: Optional[Mnojpmjdmbbfmejpflffifhffcmidifd] = None


@dataclass
class WelcomeExtensions:
    alerts: Optional[Alerts] = None
    chrome_url_overrides: Optional[TranslateSiteBlocklistWithTime] = None
    last_chrome_version: Optional[str] = None
    settings: Optional[PurpleSettings] = None


@dataclass
class Gcm:
    product_category_for_subtypes: Optional[str] = None


@dataclass
class PurpleServices:
    signin_scoped_device_id: Optional[UUID] = None


@dataclass
class WelcomeGoogle:
    services: Optional[PurpleServices] = None


@dataclass
class ComposeENudge:
    feature_enabled_time: Optional[str] = None
    show_count: Optional[int] = None
    used_count: Optional[int] = None


@dataclass
class NewBadge:
    compose_nudge: Optional[ComposeENudge] = None
    compose_proactive_nudge: Optional[ComposeENudge] = None


@dataclass
class InProductHelp:
    new_badge: Optional[NewBadge] = None
    recent_session_enabled_time: Optional[str] = None
    recent_session_start_times: Optional[List[str]] = None
    session_last_active_time: Optional[str] = None
    session_start_time: Optional[str] = None


@dataclass
class Intl:
    selected_languages: Optional[str] = None

    def field_names(self) -> List[str]:
        return list(asdict(self).keys())


@dataclass
class PerSenderTopicsToHandler:
    the_1013309121859: Optional[TranslateSiteBlocklistWithTime] = None


@dataclass
class Invalidation:
    per_sender_topics_to_handler: Optional[PerSenderTopicsToHandler] = None


@dataclass
class LanguageModelCounters:
    en: Optional[int] = None


@dataclass
class Engagement:
    schema_version: Optional[int] = None


@dataclass
class WelcomeMedia:
    engagement: Optional[Engagement] = None


@dataclass
class MediaRouter:
    enable_media_router: Optional[bool] = None
    receiver_id_hash_token: Optional[str] = None


@dataclass
class NTP:
    num_personal_suggestions: Optional[int] = None


@dataclass
class PasswordManager:
    autofillable_credentials_account_store_login_database: Optional[bool] = None
    autofillable_credentials_profile_store_login_database: Optional[bool] = None
    relaunch_chrome_bubble_dismissed_counter: Optional[int] = None


@dataclass
class M1:
    ad_measurement_enabled: Optional[bool] = None
    fledge_enabled: Optional[bool] = None
    topics_enabled: Optional[bool] = None


@dataclass
class PrivacySandbox:
    first_party_sets_data_access_allowed_initialized: Optional[bool] = None
    first_party_sets_enabled: Optional[bool] = None
    m1: Optional[M1] = None


@dataclass
class NextInstallTextAnimation:
    delay: Optional[str] = None
    last_shown: Optional[str] = None


@dataclass
class SettingHTTPSGithubCOM:
    could_show_banner_events: Optional[float] = None
    next_install_text_animation: Optional[NextInstallTextAnimation] = None


@dataclass
class PurpleSetting:
    https_github_com: Optional[SettingHTTPSGithubCOM] = None


@dataclass
class AppBannerHTTPSGithubCOM443:
    last_modified: Optional[str] = None
    setting: Optional[PurpleSetting] = None


@dataclass
class AppBanner:
    https_github_com_443: Optional[AppBannerHTTPSGithubCOM443] = None


@dataclass
class HTTPSGithubCOMSetting:
    farbling_token: Optional[str] = None


@dataclass
class HTTPSGithubCOM:
    last_modified: Optional[str] = None
    setting: Optional[HTTPSGithubCOMSetting] = None


@dataclass
class BraveShieldsMetadata:
    https_github_com: Optional[HTTPSGithubCOM] = None


@dataclass
class FluffySetting:
    has_high_score: Optional[bool] = None
    last_media_playback_time: Optional[int] = None
    media_playbacks: Optional[int] = None
    visits: Optional[int] = None


@dataclass
class MediaEngagementHTTPSGithubCOM443:
    expiration: Optional[str] = None
    last_modified: Optional[str] = None
    lifetime: Optional[str] = None
    setting: Optional[FluffySetting] = None


@dataclass
class MediaEngagement:
    https_github_com_443: Optional[MediaEngagementHTTPSGithubCOM443] = None


@dataclass
class TentacledSetting:
    last_engagement_time: Optional[float] = None
    last_shortcut_launch_time: Optional[int] = None
    points_added_today: Optional[int] = None
    raw_score: Optional[float] = None


@dataclass
class SiteEngagementHTTPSGithubCOM443:
    last_modified: Optional[str] = None
    setting: Optional[TentacledSetting] = None


@dataclass
class SiteEngagement:
    https_github_com_443: Optional[SiteEngagementHTTPSGithubCOM443] = None


@dataclass
class Exceptions:
    the_3_pcd_heuristics_grants: Optional[TranslateSiteBlocklistWithTime] = None
    the_3_pcd_support: Optional[TranslateSiteBlocklistWithTime] = None
    abusive_notification_permissions: Optional[TranslateSiteBlocklistWithTime] = None
    access_to_get_all_screens_media_in_session: Optional[TranslateSiteBlocklistWithTime] = None
    anti_abuse: Optional[TranslateSiteBlocklistWithTime] = None
    app_banner: Optional[AppBanner] = None
    ar: Optional[TranslateSiteBlocklistWithTime] = None
    are_suspicious_notifications_allowlisted_by_user: Optional[TranslateSiteBlocklistWithTime] = None
    auto_picture_in_picture: Optional[TranslateSiteBlocklistWithTime] = None
    auto_select_certificate: Optional[TranslateSiteBlocklistWithTime] = None
    automatic_downloads: Optional[TranslateSiteBlocklistWithTime] = None
    automatic_fullscreen: Optional[TranslateSiteBlocklistWithTime] = None
    autoplay: Optional[TranslateSiteBlocklistWithTime] = None
    background_sync: Optional[TranslateSiteBlocklistWithTime] = None
    bluetooth_chooser_data: Optional[TranslateSiteBlocklistWithTime] = None
    bluetooth_guard: Optional[TranslateSiteBlocklistWithTime] = None
    bluetooth_scanning: Optional[TranslateSiteBlocklistWithTime] = None
    brave_shields: Optional[TranslateSiteBlocklistWithTime] = None
    brave_shields_metadata: Optional[BraveShieldsMetadata] = None
    brave_speedreader: Optional[TranslateSiteBlocklistWithTime] = None
    brave_ethereum: Optional[TranslateSiteBlocklistWithTime] = None
    brave_google_sign_in: Optional[TranslateSiteBlocklistWithTime] = None
    brave_localhost_access: Optional[TranslateSiteBlocklistWithTime] = None
    brave_open_ai_chat: Optional[TranslateSiteBlocklistWithTime] = None
    brave_remember_1_p_storage: Optional[TranslateSiteBlocklistWithTime] = None
    brave_solana: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_audio: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_canvas: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_device_memory: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_event_source_pool: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_font: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_hardware_concurrency: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_keyboard: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_language: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_media_devices: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_none: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_plugins: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_screen: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_speech_synthesis: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_usb_device_serial_number: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_user_agent: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_web_sockets_pool: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_webgl: Optional[TranslateSiteBlocklistWithTime] = None
    brave_webcompat_webgl2: Optional[TranslateSiteBlocklistWithTime] = None
    camera_pan_tilt_zoom: Optional[TranslateSiteBlocklistWithTime] = None
    captured_surface_control: Optional[TranslateSiteBlocklistWithTime] = None
    client_hints: Optional[TranslateSiteBlocklistWithTime] = None
    clipboard: Optional[TranslateSiteBlocklistWithTime] = None
    controlled_frame: Optional[TranslateSiteBlocklistWithTime] = None
    cookie_controls_metadata: Optional[TranslateSiteBlocklistWithTime] = None
    cookies: Optional[TranslateSiteBlocklistWithTime] = None
    cosmetic_filtering: Optional[TranslateSiteBlocklistWithTime] = None
    direct_sockets: Optional[TranslateSiteBlocklistWithTime] = None
    direct_sockets_private_network_access: Optional[TranslateSiteBlocklistWithTime] = None
    display_media_system_audio: Optional[TranslateSiteBlocklistWithTime] = None
    disruptive_notification_permissions: Optional[TranslateSiteBlocklistWithTime] = None
    durable_storage: Optional[TranslateSiteBlocklistWithTime] = None
    fedcm_idp_registration: Optional[TranslateSiteBlocklistWithTime] = None
    fedcm_idp_signin: Optional[TranslateSiteBlocklistWithTime] = None
    fedcm_share: Optional[TranslateSiteBlocklistWithTime] = None
    file_system_access_chooser_data: Optional[TranslateSiteBlocklistWithTime] = None
    file_system_access_extended_permission: Optional[TranslateSiteBlocklistWithTime] = None
    file_system_access_restore_permission: Optional[TranslateSiteBlocklistWithTime] = None
    file_system_last_picked_directory: Optional[TranslateSiteBlocklistWithTime] = None
    file_system_read_guard: Optional[TranslateSiteBlocklistWithTime] = None
    file_system_write_guard: Optional[TranslateSiteBlocklistWithTime] = None
    fingerprinting_v2: Optional[TranslateSiteBlocklistWithTime] = None
    formfill_metadata: Optional[TranslateSiteBlocklistWithTime] = None
    geolocation: Optional[TranslateSiteBlocklistWithTime] = None
    hand_tracking: Optional[TranslateSiteBlocklistWithTime] = None
    hid_chooser_data: Optional[TranslateSiteBlocklistWithTime] = None
    hid_guard: Optional[TranslateSiteBlocklistWithTime] = None
    http_upgradable_resources: Optional[TranslateSiteBlocklistWithTime] = None
    http_allowed: Optional[TranslateSiteBlocklistWithTime] = None
    https_upgrades: Optional[TranslateSiteBlocklistWithTime] = None
    https_enforced: Optional[TranslateSiteBlocklistWithTime] = None
    idle_detection: Optional[TranslateSiteBlocklistWithTime] = None
    images: Optional[TranslateSiteBlocklistWithTime] = None
    important_site_info: Optional[TranslateSiteBlocklistWithTime] = None
    insecure_private_network: Optional[TranslateSiteBlocklistWithTime] = None
    intent_picker_auto_display: Optional[TranslateSiteBlocklistWithTime] = None
    javascript: Optional[TranslateSiteBlocklistWithTime] = None
    javascript_jit: Optional[TranslateSiteBlocklistWithTime] = None
    javascript_optimizer: Optional[TranslateSiteBlocklistWithTime] = None
    keyboard_lock: Optional[TranslateSiteBlocklistWithTime] = None
    legacy_cookie_access: Optional[TranslateSiteBlocklistWithTime] = None
    legacy_cookie_scope: Optional[TranslateSiteBlocklistWithTime] = None
    local_fonts: Optional[TranslateSiteBlocklistWithTime] = None
    local_network_access: Optional[TranslateSiteBlocklistWithTime] = None
    media_engagement: Optional[MediaEngagement] = None
    media_stream_camera: Optional[TranslateSiteBlocklistWithTime] = None
    media_stream_mic: Optional[TranslateSiteBlocklistWithTime] = None
    midi_sysex: Optional[TranslateSiteBlocklistWithTime] = None
    mixed_script: Optional[TranslateSiteBlocklistWithTime] = None
    nfc_devices: Optional[TranslateSiteBlocklistWithTime] = None
    notification_interactions: Optional[TranslateSiteBlocklistWithTime] = None
    notification_permission_review: Optional[TranslateSiteBlocklistWithTime] = None
    notifications: Optional[TranslateSiteBlocklistWithTime] = None
    password_protection: Optional[TranslateSiteBlocklistWithTime] = None
    payment_handler: Optional[TranslateSiteBlocklistWithTime] = None
    permission_autoblocking_data: Optional[TranslateSiteBlocklistWithTime] = None
    permission_autorevocation_data: Optional[TranslateSiteBlocklistWithTime] = None
    pointer_lock: Optional[TranslateSiteBlocklistWithTime] = None
    popups: Optional[TranslateSiteBlocklistWithTime] = None
    private_network_chooser_data: Optional[TranslateSiteBlocklistWithTime] = None
    private_network_guard: Optional[TranslateSiteBlocklistWithTime] = None
    protocol_handler: Optional[TranslateSiteBlocklistWithTime] = None
    reduced_accept_language: Optional[TranslateSiteBlocklistWithTime] = None
    referrers: Optional[TranslateSiteBlocklistWithTime] = None
    safe_browsing_url_check_data: Optional[TranslateSiteBlocklistWithTime] = None
    sensors: Optional[TranslateSiteBlocklistWithTime] = None
    serial_chooser_data: Optional[TranslateSiteBlocklistWithTime] = None
    serial_guard: Optional[TranslateSiteBlocklistWithTime] = None
    shields_ads: Optional[TranslateSiteBlocklistWithTime] = None
    shields_cookies_v3: Optional[TranslateSiteBlocklistWithTime] = None
    site_engagement: Optional[SiteEngagement] = None
    sound: Optional[TranslateSiteBlocklistWithTime] = None
    speaker_selection: Optional[TranslateSiteBlocklistWithTime] = None
    ssl_cert_decisions: Optional[TranslateSiteBlocklistWithTime] = None
    storage_access: Optional[TranslateSiteBlocklistWithTime] = None
    storage_access_header_origin_trial: Optional[TranslateSiteBlocklistWithTime] = None
    subresource_filter: Optional[TranslateSiteBlocklistWithTime] = None
    subresource_filter_data: Optional[TranslateSiteBlocklistWithTime] = None
    third_party_storage_partitioning: Optional[TranslateSiteBlocklistWithTime] = None
    top_level_3_pcd_origin_trial: Optional[TranslateSiteBlocklistWithTime] = None
    top_level_3_pcd_support: Optional[TranslateSiteBlocklistWithTime] = None
    top_level_storage_access: Optional[TranslateSiteBlocklistWithTime] = None
    trackers: Optional[TranslateSiteBlocklistWithTime] = None
    tracking_protection: Optional[TranslateSiteBlocklistWithTime] = None
    unused_site_permissions: Optional[TranslateSiteBlocklistWithTime] = None
    usb_chooser_data: Optional[TranslateSiteBlocklistWithTime] = None
    usb_guard: Optional[TranslateSiteBlocklistWithTime] = None
    vr: Optional[TranslateSiteBlocklistWithTime] = None
    web_app_installation: Optional[TranslateSiteBlocklistWithTime] = None
    webid_api: Optional[TranslateSiteBlocklistWithTime] = None
    webid_auto_reauthn: Optional[TranslateSiteBlocklistWithTime] = None
    window_placement: Optional[TranslateSiteBlocklistWithTime] = None


@dataclass
class ContentSettings:
    exceptions: Optional[Exceptions] = None
    pref_version: Optional[int] = None


@dataclass
class Managed:
    locally_parent_approved_extensions: Optional[TranslateSiteBlocklistWithTime] = None
    locally_parent_approved_extensions_migration_state: Optional[int] = None


@dataclass
class Profile:
    avatar_index: Optional[int] = None
    background_password_check: Optional[dict] = None
    content_settings: Optional[ContentSettings] = None
    created_by_version: Optional[str] = None
    creation_time: Optional[str] = None
    did_work_around_bug_364820109_default: Optional[bool] = None
    did_work_around_bug_364820109_exceptions: Optional[bool] = None
    exit_type: Optional[str] = None
    family_member_role: Optional[str] = None
    last_engagement_time: Optional[str] = None
    last_time_obsolete_http_credentials_removed: Optional[float] = None
    last_time_password_store_metrics_reported: Optional[float] = None
    managed: Optional[Managed] = None
    managed_user_id: Optional[str] = None
    name: Optional[str] = None
    password_hash_data_list: Optional[List[Any]] = None
    were_old_google_logins_removed: Optional[bool] = None

    def field_names(self) -> List[str]:
        return list(asdict(self).keys())


@dataclass
class AdBlock:
    developer_mode: Optional[str] = None


@dataclass
class MacsBrave:
    ad_block: Optional[AdBlock] = None


@dataclass
class MacsBrowser:
    show_home_button: Optional[str] = None


@dataclass
class DefaultSearchProviderData:
    template_url_data: Optional[str] = None


@dataclass
class EnterpriseSignin:
    policy_recovery_token: Optional[str] = None


@dataclass
class FluffySettings:
    ahfgeienlihckogmohjhadlkjgocpleb: Optional[str] = None
    mhjfbmdgcfjbbpaeojofohoefgiehjai: Optional[str] = None
    mnojpmjdmbbfmejpflffifhffcmidifd: Optional[str] = None


@dataclass
class MacsExtensions:
    settings: Optional[FluffySettings] = None
    ui: Optional[AdBlock] = None


@dataclass
class FluffyServices:
    account_id: Optional[str] = None
    last_signed_in_username: Optional[str] = None
    last_username: Optional[str] = None


@dataclass
class MacsGoogle:
    services: Optional[FluffyServices] = None


@dataclass
class MacsMedia:
    storage_id_salt: Optional[str] = None


@dataclass
class Prefs:
    preference_reset_time: Optional[str] = None


@dataclass
class MacsSafebrowsing:
    incidents_sent: Optional[str] = None


@dataclass
class Session:
    restore_on_startup: Optional[str] = None
    startup_urls: Optional[str] = None


@dataclass
class Macs:
    brave: Optional[MacsBrave] = None
    browser: Optional[MacsBrowser] = None
    default_search_provider_data: Optional[DefaultSearchProviderData] = None
    enterprise_signin: Optional[EnterpriseSignin] = None
    extensions: Optional[MacsExtensions] = None
    google: Optional[MacsGoogle] = None
    homepage: Optional[str] = None
    homepage_is_newtabpage: Optional[str] = None
    media: Optional[MacsMedia] = None
    pinned_tabs: Optional[str] = None
    prefs: Optional[Prefs] = None
    safebrowsing: Optional[MacsSafebrowsing] = None
    search_provider_overrides: Optional[str] = None
    session: Optional[Session] = None


@dataclass
class Protection:
    macs: Optional[Macs] = None


@dataclass
class The0:
    the_12: Optional[List[str]] = None


@dataclass
class EventTimestamps:
    the_0: Optional[The0] = None


@dataclass
class WelcomeSafebrowsing:
    event_timestamps: Optional[EventTimestamps] = None
    metrics_last_log_time: Optional[str] = None
    scout_reporting_enabled_when_deprecated: Optional[bool] = None


@dataclass
class UnusedSitePermissionsRevocation:
    migration_completed: Optional[bool] = None


@dataclass
class SafetyHub:
    unused_site_permissions_revocation: Optional[UnusedSitePermissionsRevocation] = None


@dataclass
class SavedTabGroups:
    did_enable_shared_tab_groups_in_last_session: Optional[bool] = None
    specifics_to_data_migration: Optional[bool] = None


@dataclass
class Savefile:
    default_directory: Optional[str] = None


@dataclass
class EventLog:
    crashed: Optional[bool] = None
    time: Optional[str] = None
    type: Optional[int] = None
    restore_browser: Optional[bool] = None
    synchronous: Optional[bool] = None
    errored_reading: Optional[bool] = None
    tab_count: Optional[int] = None
    window_count: Optional[int] = None
    did_schedule_command: Optional[bool] = None
    first_session_service: Optional[bool] = None


@dataclass
class Sessions:
    event_log: Optional[List[EventLog]] = None
    session_data_status: Optional[int] = None


@dataclass
class Signin:
    allowed: Optional[bool] = None
    cookie_clear_on_exit_migration_notice_complete: Optional[bool] = None


@dataclass
class Spellcheck:
    dictionaries: Optional[List[str]] = None
    dictionary: Optional[str] = None


@dataclass
class Sync:
    data_type_status_for_sync_to_signin: Optional[Dict[str, bool]] = None
    encryption_bootstrap_token_per_account_migration_done: Optional[bool] = None
    feature_status_for_sync_to_signin: Optional[int] = None
    passwords_per_account_pref_migration_done: Optional[bool] = None


@dataclass
class Toolbar:
    pinned_chrome_labs_migration_complete: Optional[bool] = None


@dataclass
class DailyMetricsHTTPSGithubCOM:
    background_duration_sec: Optional[int] = None
    captures_links: Optional[bool] = None
    effective_display_mode: Optional[int] = None
    foreground_duration_sec: Optional[int] = None
    installed: Optional[bool] = None
    num_sessions: Optional[int] = None
    promotable: Optional[bool] = None


@dataclass
class DailyMetrics:
    https_github_com: Optional[DailyMetricsHTTPSGithubCOM] = None


@dataclass
class WebApps:
    daily_metrics: Optional[DailyMetrics] = None
    daily_metrics_date: Optional[str] = None
    did_migrate_default_chrome_apps: Optional[List[str]] = None
    last_preinstall_synchronize_version: Optional[int] = None


@dataclass
class Welcome:
    alternate_error_pages: Optional[AlternateErrorPages] = None
    apps: Optional[Apps] = None
    autocomplete: Optional[Autocomplete] = None
    autofill: Optional[Autofill] = None
    brave: Optional[WelcomeBrave] = None
    brave_shields: Optional[BraveShields] = None
    brave_sync_v2: Optional[BraveSyncV2] = None
    browser: Optional[WelcomeBrowser] = None
    commerce_daily_metrics_last_update_time: Optional[str] = None
    countryid_at_install: Optional[int] = None
    default_apps_install_state: Optional[int] = None
    default_search_provider: Optional[DefaultSearchProvider] = None
    domain_diversity: Optional[DomainDiversity] = None
    download_bubble: Optional[DownloadBubble] = None
    enterprise_profile_guid: Optional[UUID] = None
    ephemeral_storage: Optional[EphemeralStorage] = None
    extensions: Optional[WelcomeExtensions] = None
    gcm: Optional[Gcm] = None
    google: Optional[WelcomeGoogle] = None
    in_product_help: Optional[InProductHelp] = None
    intl: Optional[Intl] = None
    invalidation: Optional[Invalidation] = None
    language_model_counters: Optional[LanguageModelCounters] = None
    media: Optional[WelcomeMedia] = None
    media_router: Optional[MediaRouter] = None
    ntp: Optional[NTP] = None
    password_manager: Optional[PasswordManager] = None
    pinned_tabs: Optional[List[Any]] = None
    privacy_sandbox: Optional[PrivacySandbox] = None
    profile: Optional[Profile] = None
    protection: Optional[Protection] = None
    safebrowsing: Optional[WelcomeSafebrowsing] = None
    safety_hub: Optional[SafetyHub] = None
    saved_tab_groups: Optional[SavedTabGroups] = None
    savefile: Optional[Savefile] = None
    sessions: Optional[Sessions] = None
    signin: Optional[Signin] = None
    spellcheck: Optional[Spellcheck] = None
    sync: Optional[Sync] = None
    syncing_theme_prefs_migrated_to_non_syncing: Optional[bool] = None
    tab_group_saves_ui_update_migrated: Optional[bool] = None
    toolbar: Optional[Toolbar] = None
    total_passwords_available_for_account: Optional[int] = None
    total_passwords_available_for_profile: Optional[int] = None
    translate_site_blacklist: Optional[List[Any]] = None
    translate_site_blocklist_with_time: Optional[TranslateSiteBlocklistWithTime] = None
    web_apps: Optional[WebApps] = None

    def field_names(self) -> List[str]:
        return list(asdict(self).keys())