import subprocess
import time
from dataclasses import replace
from json import dumps as json_dumps
from pathlib import Path
from time import sleep
from typing import Any, Dict

import psutil

from brave_profiles_main.QuickType_for_preferences import Intl
from brave_profiles_main.QuickType_for_preferences import Profile as QProfile
from brave_profiles_main.config import BravePaths
from brave_profiles_main.file_utils import copy_file, make_folder_with_gitkeep
from brave_profiles_main.json_utils import read_json_file, write_json_file, validate_json_dict
from brave_profiles_main.profile_types import Profile

# from brave_profiles_main.PathEnum import PathEnum

BASE_DIR = Path(__file__).resolve().parent


def is_brave_process(proc):
    try:
        name = proc.name().lower()
        cmdline = " ".join(proc.cmdline()).lower()
        if "brave" in name or "brave" in cmdline:
            if "python" not in name and "python" not in cmdline:
                return True
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        pass
    return False


def kill_brave():
    print("Killing Brave processes...")
    brave_found = False
    for proc in psutil.process_iter(["pid", "name", "cmdline"]):
        if is_brave_process(proc):
            try:
                print(f"Terminating PID {proc.pid}: {proc.name()}")
                proc.terminate()
                brave_found = True
            except psutil.NoSuchProcess:
                pass
    if not brave_found:
        print("No Brave processes found.")


def wait_for_brave_shutdown(timeout=10):
    print("Waiting for Brave to fully shut down...")
    for _ in range(timeout * 10):
        if not any(is_brave_process(p) for p in psutil.process_iter()):
            print("✅ Brave is fully closed.")
            return True
        time.sleep(0.1)
    print("❌ Timeout: Brave still running.")
    return False


def brave_shut_down() -> bool:
    """
    Manage Brave browser process - check if running or attempt to close.

    Args:
        action: "check" to check if running, "close" to attempt closing

    Returns:
        bool: True if running (for check) or successfully closed (for close)
    """
    try:
        output = subprocess.check_output("tasklist", shell=True, text=True)

        if "brave.exe" in output.lower():
            print("✅ Brave is running.Closing....")
            result = subprocess.run(
                ["taskkill", "/f", "/im", "brave.exe"],
                shell=True,
                capture_output=True,
                text=True,
                check=False,
            )
            sleep(1)
            if result.returncode == 0:
                print("✅ Brave closed successfully.")
                return True
            else:
                print("⚠️ Could not close Brave.")
                print("Output:", result.stdout.strip())
                print("Error:", result.stderr.strip())
                return False
        else:
            print("ℹ️ Brave is not running.")
            return True  # Not running is considered "success" for shutdown

    except Exception as e:
        print("❌ Error closing Brave:", e)
        return False


def update_profile_name_in_dir_preferences(
    profile_dict: Dict[str, Profile] = {},
    view_only: bool = True,
    paths: BravePaths = BravePaths(),
) -> Dict[str, str]:
    """
    profile_dict: Dict[str, Profile]
    Update the profile name in Preferences file.

    Args:
        profile_vs_new_name: Dictionary mapping profile directories to new names
        view_only: If True, only show what would be changed without making changes

    Returns:
        Dict[str, str]: Dictionary mapping profile directories to their current/updated names
    """
    # if brave_shut_down():
    #     return {}

    preferences_backup_folder = paths.preferences_backup_folder
    # print(f"prefs_folder: {prefs_folder.is_dir()}")
    brave_user_folder = paths.brave_user_folder
    profile_paths_on_disc = [
        x
        for x in brave_user_folder.glob("*")
        if x.is_dir() and x.stem.startswith("Profile ")
    ]
    profiles_on_disc = [x.stem for x in profile_paths_on_disc]
    profiles_on_disc.append("Default")
    # profiles = [x for x in profile_dict.values() if x.profile_dir in profile_paths_on_disc]

    profile_dir_vs_name = {}
    name_sync_dict_map = {}
    for name, profile in profile_dict.items():
        profile_dir = profile.profile_dir
        this_profile_prefs_path = (
            brave_user_folder / profile.profile_dir / "Preferences"
        )
        if this_profile_prefs_path.is_file():
            preferences_data = read_json_file(this_profile_prefs_path)
            preferences = validate_json_dict(preferences_data, this_profile_prefs_path)
        else:
            print(f"⚠️ Preferences file not found for {profile_dir}")
            profile_dir_vs_name[profile_dir] = "NO Preferences"
            continue
        if profile_dir == "Default":
            backup_file = (
                preferences_backup_folder
                / f"Default_{profile.name}_orig_backup_pretty.json"
            )
        else:
            backup_file = (
                preferences_backup_folder / f"{profile.name}_orig_backup_pretty.json"
            )
        write_json_file(file_path=backup_file, data=preferences, compact=False)

        print(f"profile_dir: {profile_dir}, profile_name: {profile.name}")
        if profile_dir not in profiles_on_disc:
            profile_dir_vs_name[profile_dir] = "NOT FOUND"
            continue

        wanted_fields = [
            ("profile", QProfile),
            ("intl", Intl),
        ]
        field_obj_map: dict[str, Any] = {}
        for item in wanted_fields:
            new_obj = item[1]()
            new_obj_fields = new_obj.field_names()
            current_value_in_preferences = preferences.get(item[0], {})
            new_dict = {
                k: v
                for k, v in current_value_in_preferences.items()
                if k in new_obj_fields
            }
            new_obj = replace(new_obj, **new_dict)
            field_obj_map[item[0]] = new_obj

        current_name = field_obj_map["profile"].name
        print(f"Current name: {current_name}\n")
        current_avatar_index = field_obj_map["profile"].avatar_index
        print(f"Current avatar index: {current_avatar_index}\n")
        print(f"Current profile avatar idx: {profile.avatar_icon.split('_')[-1]}\n")
        current_intl = field_obj_map["intl"].selected_languages
        print(f"Current intl: {current_intl}\n\n")
        syncs_folder = make_folder_with_gitkeep(folder=paths.base_dir / "syncs")
        write_json_file(
            file_path=syncs_folder / f"{profile.name}_sync",
            data=preferences.get("sync", {}),
            compact=False,
        )
        name_sync_dict_map[profile.name] = preferences.get("sync", {})

        if view_only:
            print(f"Name in preferences {current_name} NOT updated to: {profile.name}")
            continue
        # if profile_dir == "Default":
        #     print(f"⚠️ Skipping Default profile editing: {profile_dir}")
        #     continue
        preferences["profile"]["name"] = profile.name
        preferences["profile"]["avatar_index"] = profile.avatar_icon.split("_")[-1]
        preferences["intl"]["selected_languages"] = "fi,en-US,en"
        profile_dir_vs_name[profile_dir] = profile.name

        backup_new_file_name = (
            preferences_backup_folder / f"{profile.name}_new_backup_pretty.json"
        )
        write_json_file(file_path=backup_new_file_name, data=preferences, compact=False)
        new_json_for_brave = write_json_file(
            file_path=this_profile_prefs_path, data=preferences, compact=True
        )
        if new_json_for_brave:
            print(
                f"✅ Updated {profile_dir} profile name in Preferences: '{current_name}' → '{profile.name}'"
            )
            print(f"Backup of new Preferences saved to: {backup_new_file_name}\n")
        else:
            print(f"⚠️ Error writing Preferences file for {profile_dir}")
            profile_dir_vs_name[profile_dir] = "Error writing Preferences"
            continue
    seen = set()
    unique = []

    for d in name_sync_dict_map:
        j = json_dumps(d, sort_keys=True)
        if j not in seen:
            seen.add(j)
            unique.append(d)

    print(f"unique syncs: {unique}")

    return profile_dir_vs_name


def force_update_local_state(
    profile_dict: Dict[str, Profile] = {},
    view_only: bool = True,
    paths: BravePaths = BravePaths(),
) -> Dict[str, str]:
    """
    Force update profile names in Local State file.

    Args:
        profile_vs_name: Dictionary mapping profile directories to new names
        view_only: If True, only show what would be changed without making changes

    Returns:
        Dict[str, str]: Dictionary mapping profile directories to their current/updated names
    """
    local_state: Dict[str, Any] = {}
    brave_local_state_file_path = paths.brave_local_state_file_path
    avatar_dest_folder = paths.brave_avatar_dest_folder
    try:
        if brave_local_state_file_path.is_file():
            local_state_data = read_json_file(brave_local_state_file_path)
            local_state = validate_json_dict(local_state_data, brave_local_state_file_path)
            print(f"brave_local_state_path: {brave_local_state_file_path} found.")
    except FileNotFoundError:
        print("❌ Brave Local State file not found.")
        return {}
    # Backup original file
    local_state_backup_folder = paths.local_state_backup_folder
    copy_success = copy_file(
        source_path=brave_local_state_file_path,
        dest_dir=local_state_backup_folder,
        new_name=brave_local_state_file_path.name,
    )
    json_success = write_json_file(
        file_path=local_state_backup_folder / "Local State backup pretty.json",
        data=local_state,
        compact=False,
    )
    if not copy_success or not json_success:
        print("⚠️ Error creating backup of Brave Local State file")
        return {}
    info_cache_profiles = local_state.get("profile", {}).get("info_cache", {})
    updated = 0
    profile_dir_vs_name = {}

    for profile_name, profile in profile_dict.items():
        print(f"profile_name: {profile.name}, profile_dir: {profile.profile_dir}")
        if profile.profile_dir not in info_cache_profiles:
            print(f"⚠️  {profile_name} not found in info_cache — maybe unused yet?")
            profile_dir_vs_name[profile.profile_dir] = "NOT FOUND"
            continue

        current_name = info_cache_profiles[profile.profile_dir].get("name", "")
        current_icon = info_cache_profiles[profile.profile_dir].get("avatar_icon", "")

        if view_only:
            print(f"🔁 {profile.name} Not updated: '{current_name}' → '{profile.name}'")
            print(f"Current icon: {current_icon}")
            continue

        info_cache_profiles[profile.profile_dir]["name"] = profile.name
        info_cache_profiles[profile.profile_dir]["avatar_icon"] = profile.avatar_icon
        info_cache_profiles[profile.profile_dir]["is_using_default_name"] = False
        info_cache_profiles[profile.profile_dir]["is_using_default_avatar"] = False
        if profile.profile_dir != "Default":
            if copy_file(
                source_path=profile.png_source_file_path,
                dest_dir=avatar_dest_folder,
                new_name=profile.png_new_name,
                message=False,
            ):
                print(
                    f"✅ Copied {profile.png_source_file_path} to: {avatar_dest_folder / profile.png_new_name}"
                )
            else:
                print(
                    f"⚠️ Error copying {profile.png_source_file_path} to: {avatar_dest_folder / profile.png_new_name}"
                )
                profile_dir_vs_name[profile.profile_dir] = "Error copying avatar"
                continue

        print(
            f"🔁 {profile.profile_dir} updated: '{current_name}' → '{profile.name}' \n"
        )
        updated += 1
        profile_dir_vs_name[profile.profile_dir] = profile.name

    if updated and not view_only:
        if write_json_file(
            file_path=brave_local_state_file_path, data=local_state, compact=True
        ):
            print(f"\n✅ Updated {updated} profile names in Local State.")
            if write_json_file(
                file_path=local_state_backup_folder
                / "Local State backup pretty_after.json",
                data=local_state,
                compact=False,
            ):
                print(
                    f"Backup of Local State saved to: {local_state_backup_folder / 'Local State backup pretty_after.json'}"
                )
            else:
                print("⚠️ Error writing backup of Local State file")
        else:
            print("Error writing Brave Local State file")
    else:
        print("ℹ️ No changes made.\n\n")

    return profile_dir_vs_name


def main() -> None:
    """Main function for testing the profile management functionality."""
    print("Running")
    print(__file__)
    print("" + "!" * 20)


if __name__ == "__main__":
    main()
